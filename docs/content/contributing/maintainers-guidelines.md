---
title: "Traefik Maintainer's Guidelines Documentation"
description: "Interested in contributing more to the community and becoming a Traefik Proxy maintainer? Read the guide to becoming a part of the core team."
---

# Maintainer's Guidelines

![Maintainer's Guidelines](../assets/img/maintainers-guidelines.png)

Welcome to the Traefik Community.

We are strongly promoting a philosophy of openness and sharing,
and firmly standing against the elitist closed approach.
Being part of the core team should be accessible to anyone motivated
and wants to be part of that journey!

## Becoming a Maintainer

Before a contributor becomes a maintainer, they should meet the following requirements:

- The contributor enabled [2FA](https://docs.github.com/en/authentication/securing-your-account-with-two-factor-authentication-2fa/configuring-two-factor-authentication) on their GitHub account

- The contributor showed a consistent pattern of helpful, non-threatening, and friendly behavior towards other community members in the past.

- The contributor has read and accepted the maintainer's guidelines.

The contributor should also meet one or several of the following requirements:

- The contributor has opened and successfully run medium to large PR’s in the past 6 months.

- The contributor has participated in multiple code reviews of other PR’s,
  including those of other maintainers and contributors.

- The contributor is active on Traefik Community forums
  or other technical forums/boards, such as K8S Slack, Reddit, StackOverflow, and Hacker News.

Any existing active maintainer can create an issue to discuss promoting a contributor to maintainer. 
Other maintainers can vote on the issue, and if the quorum is reached, the contributor is promoted to maintainer.
If the quorum is not reached within one month after the issue is created, it is closed.

## Maintainer's Responsibilities and Privileges

As a maintainer, you are granted a vote for the following:

- [PR review](https://github.com/traefik/contributors-guide/blob/master/pr_guidelines.md).

- [Design review](https://github.com/traefik/contributors-guide/blob/master/proposals.md).

- [Proposals](https://github.com/traefik/contributors-guide/blob/master/proposals.md).

Maintainers are also added to the maintainer's Discord server where happens the [issue triage](https://github.com/traefik/contributors-guide/blob/master/issue_triage.md)
and appear on the [Maintainers](maintainers.md) page.

As a maintainer, you should: 

- Prioritize PR reviews, design reviews, and issue triage above any other task. 

Making sure contributors and community members are listened to and have an impact on the project is essential to keeping the project active and develop a thriving community.

- Prioritize helping contributors reaching the expecting quality level over rewriting contributions.

Any triage activity on issues and PRs (e.g. labels, marking messages as off-topic, refusing, marking duplicates) should result from a collective decision to ensure knowledge is shared among maintainers.

## Communicating

- All of our maintainers are added to the Traefik Maintainers Discord server that belongs to Traefik labs.
  Having the team in one place helps us to communicate effectively.
  Maintainers can discuss issues, pull requests, enhancements more efficiently
  and get the feedback almost immediately.
  Fewer blockers mean more fun and engaging work.

- Every decision made on the discord server among maintainers is documented so it's visible to the rest of the community.

- Maintainers express their opinions on issues and reviews. 
  It is fine to have different point of views. 
  We encourage active and open conversations which goals are to improve Traefik.

- When discussing issues and proposals, maintainers should share as much information as possible to help solve the issue.

## Maintainers Activity

In order to keep the core team efficient and dynamic,
maintainers' activity and involvement will be reviewed on a regular basis.

- Has the maintainer engaged with the team and the community by meeting two or more of these benchmarks in the past six months?

    - Has the maintainer participated in at least two or three maintainer meetings?

    - Substantial review of at least one or two PRs from either contributors or maintainers.

    - Opened at least one or two bug fixes or feature request PRs
      that were eventually merged (or on a trajectory for merge).

    - Substantial participation in the Help Wanted program (answered questions, helped identify issues, applied guidelines from the Help Wanted guide to open issues).

    - Substantial participation with the community in general.

- Has the maintainer shown a consistent pattern of helpful,
  non-threatening,
  and friendly behavior towards other people on the maintainer team and with our community?

## Additional Comments for Maintainers (that should apply to any contributor)

- Be respectful with other maintainers and other community members.

- Be open minded when participating in conversations: try to put yourself in others’ shoes.

- Keep the communication public -
  if anyone tries to communicate with you directly,
  ask politely to move the conversation to a public communication channel.

- Stay away from defensive comments.

- Please try to express your thoughts clearly enough
  and note that some of us are not native English speakers.
  Try to rephrase your sentences, avoiding mental shortcuts;
  none of us is able to predict anyone's thoughts.

- Be proactive.

- Emoji are fine,
  but if you express yourself clearly enough they are not necessary.
  They will not replace good communication.

- Embrace mentorship: help others grow and match the quality level we strive for.

- Keep in mind that we all have the same goal: improve the project.
