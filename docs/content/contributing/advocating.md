---
title: "Traefik Advocation Documentation"
description: "There are many ways to contribute to Traefik Proxy. Let us know if you’re talking about Traefik, and we'll promote your enthusiasm!"
---

# Advocating

Spread the Love & Tell Us About It
{: .subtitle }

Traefik Proxy was started by the community and for the community.
You can contribute to the Traefik community in three main ways:

**Spread the word!** Guides, videos, blog posts, how-to articles, and showing off your network design all help spread the word about Traefik Proxy
and teach others in the community how to best implement it.
It always sparks joy when users share how Traefik Proxy helps them solve their problems.
If you are talking about Traefik Proxy, [let us know](https://traefik.io/submit-my-contribution/) and we will promote your work and reward your enthusiasm!
If you are giving a talk that includes or is about Traefik Proxy, [let us know](https://traefik.io/submit-my-contribution/) and we will send you swag and stickers for your time at the conference.
If you have written about <PERSON>rae<PERSON>k or shared useful information you would like to promote, feel free to add links to the [dedicated wiki page on GitHub](https://github.com/traefik/traefik/wiki/Awesome-Traefik).

**Help community members!** Everyone needs a place to share their cool innovations or get help with that pesky bug that only a different pair of eyes seems to be able to see.
Join our [Community Forum](https://community.traefik.io/) where you can ask questions, help out other users, and share your neat configuration examples or snippets.
Top contributors will be asked to join the Ambassador program and get unique swag to celebrate!

**Build cool solutions!** Traefik Proxy would be so much better if only it had…
We love all the wonderful ideas that our users come up with, but we can only build so much.
Luckily, as an open source community, our users can help by [building awesome features](https://github.com/orgs/traefik/projects/9/views/7), enhancements, or bug fixes.
We are a big community, so we do need to prioritize a bit.
That is why we use the tag `contributor/wanted` to let you know which pull requests will make it to the front of the queue for design support and review.
Feel free to grab one of these and run with it.
Top contributors get unique swag to celebrate.
