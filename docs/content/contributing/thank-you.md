---
title: "Traefik Contribution Documentation"
description: "Thank you to all those who have contributed! Traefik Proxy is an open-source project that thrives with the support of our passionate community."
---

# Thank You!

_You_ Made It
{: .subtitle}

Traefik Proxy truly is an [open-source project](https://github.com/traefik/traefik/),
and wouldn't have become what it is today without the help of our [many contributors](https://github.com/traefik/traefik/graphs/contributors),
not accounting for people having helped with issues, tests, comments, articles, ... or just enjoy using Traefik Proxy and share with others.

So once again, thank you for your invaluable help in making Traefik such a good product!

!!! question "Where to Go Next?"
    If you want to:

    - Propose an idea, request a feature, or report a bug, 
      then read [Submitting Issues](./submitting-issues.md).
    - Discover how to make an efficient contribution,
      then read [Submitting Pull Requests](./submitting-pull-requests.md).
    - Learn how to build and test Traefik,
      then the page [Building and Testing](./building-testing.md) is for you.
    - Contribute to the documentation,
      then read the page about [Documentation](./documentation.md).
    - Understand how do we learn about Traefik usage,
      read the [Data Collection](./data-collection.md) page.
    - Spread the love about Traefik, please check the [Advocating](./advocating.md) page.
    - Learn about who are the maintainers and how they work on the project,
      read the [Maintainers](./maintainers.md) and [Maintainer Guidelines](./maintainers-guidelines.md) pages.
