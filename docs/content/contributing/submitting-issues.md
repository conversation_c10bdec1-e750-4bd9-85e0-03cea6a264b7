---
title: "Traefik Submitting Issues Documentation"
description: "Help us help you! Learn how to submit an issue, following the guidelines, so the Traefik Proxy team can help. Read the technical documentation."
---

# Submitting Issues

Help Us Help You!
{: .subtitle }

Issues are perfect for requesting a feature/enhancement or reporting a suspected bug.
We use the [GitHub issue tracker](https://github.com/traefik/traefik/issues) to keep track of issues in Traefik.

The process of sorting and checking the issues is a daunting task, and requires a lot of work.
To help maintainers (and other community members) quickly and effortlessly understand what you need,
be sure to follow the guidelines below.

!!! important "Getting Help Vs Reporting an Issue"

    The issue tracker is not a general support forum, but a place to report bugs and asks for new features.

    For end-user related support questions, try using the [Traefik Community Forum](https://community.traefik.io/)
   [![Join the chat at https://community.traefik.io/](https://img.shields.io/badge/style-register-green.svg?style=social&label=Traefik%20Community%20Forum)](https://community.traefik.io/)

## Issue Title

The title must be short and descriptive. (~60 characters)

Examples:

* Bug: Duplicate requests in access logs
* Feature: Support TCP

## Feature Request

Traefik is an open source project and aims to be the best edge router possible.

Remember when asking for new features that these must be useful to the majority (and not only useful in edge case scenarios, or hack-like setups).
Follow the [issue template](https://github.com/traefik/traefik/blob/master/.github/ISSUE_TEMPLATE/feature-request.yml) as much as possible.

Do your best to explain what you're looking for, and why it would improve Traefik for everyone.
Be detailed and share the use-case(s) to allow us to see the value of your feature request as quickly as possible.

Features with a lot of positive interaction (claps, +1s, conversation about how this would impact them) indicate higher community interest and help us to prioritize.

If you are interested in creating a PR for your feature request, let us know in the issue, so we can work with you.
It can take a lot of work to make sure a PR can integrate with our existing code and planning with the team ahead of time can make sure that your PR can be accepted and merged quickly.

## Issues or Possible Bug Reports

Follow the [issue template](https://github.com/traefik/traefik/blob/master/.github/ISSUE_TEMPLATE/bug_report.yml) as much as possible.

Explain the conditions in which you encountered the issue; what is your context?
Share any logs you may have, and make sure to share the steps it takes to reproduce your issue or bug.

Remain as clear and concise as possible.

Take time to polish the format of your message, so we'll enjoy reading it and working on it.
Help your readers focus on what matters and help them understand the structure of your message (see the [GitHub Markdown Syntax](https://docs.github.com/en/get-started/writing-on-github)).

## International English

Every maintainer / Traefik user is not a native English speaker, so if you sometimes feel that some messages sound rude, remember that it probably is a language barrier problem from someone willing to help you.
