---
title: "Getting Started with Traefik"
description: "Quick start guides for deploying Traefik in Kubernetes and Docker environments"
---

# Getting Started with Traefik

Traefik can be deployed in various environments. Choose your preferred deployment method:

- [Kubernetes Quick Start](./kubernetes.md) - Deploy Traefik using Helm
- [Docker Quick Start](./docker.md) - Deploy Traefik using Docker

Each guide will help you:

- Install Traefik
- Expose the dashboard
- Deploy a sample application
- Configure basic routing

## Before You Begin

Make sure you have the necessary prerequisites for your chosen environment:

- **Kubernetes**: A running Kubernetes cluster, Helm 3, and kubectl
- **Docker**: Docker and optionally Docker Compose
