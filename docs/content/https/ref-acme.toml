# Enable ACME (Let's Encrypt): automatic SSL.
[certificatesResolvers.myresolver.acme]

  # Email address used for registration.
  #
  # Required
  #
  email = "<EMAIL>"

  # File or key used for certificates storage.
  #
  # Required
  #
  storage = "acme.json"

  # CA server to use.
  # Uncomment the line to use Let's Encrypt's staging server,
  # leave commented to go to prod.
  #
  # Optional
  # Default: "https://acme-v02.api.letsencrypt.org/directory"
  #
  # caServer = "https://acme-staging-v02.api.letsencrypt.org/directory"

  # The certificates' duration in hours.
  # It defaults to 2160 (90 days) to follow Let's Encrypt certificates' duration.
  #
  # Optional
  # Default: 2160
  #
  # certificatesDuration=2160

  # Timeout for a complete HTTP transaction with the ACME server.
  #
  # Optional
  # Default: 2m
  #
  # clientTimeout="2m"

  # Timeout for receiving the response headers when communicating with the ACME server.
  #
  # Optional
  # Default: 30s
  #
  # clientResponseHeaderTimeout="30s"

  # Preferred chain to use.
  #
  # If the CA offers multiple certificate chains, prefer the chain with an issuer matching this Subject Common Name.
  # If no match, the default offered chain will be used.
  #
  # Optional
  # Default: ""
  #
  # preferredChain = "ISRG Root X1"

  # KeyType to use.
  #
  # Optional
  # Default: "RSA4096"
  #
  # Available values : "EC256", "EC384", "RSA2048", "RSA4096", "RSA8192"
  #
  # keyType = "RSA4096"

  # Use a TLS-ALPN-01 ACME challenge.
  #
  # Optional (but recommended)
  #
  [certificatesResolvers.myresolver.acme.tlsChallenge]

  # Use a HTTP-01 ACME challenge.
  #
  # Optional
  #
  # [certificatesResolvers.myresolver.acme.httpChallenge]

    # EntryPoint to use for the HTTP-01 challenges.
    #
    # Required
    #
    # entryPoint = "web"

  # Use a DNS-01 ACME challenge rather than HTTP-01 challenge.
  # Note: mandatory for wildcard certificate generation.
  #
  # Optional
  #
  # [certificatesResolvers.myresolver.acme.dnsChallenge]

    # DNS provider used.
    #
    # Required
    #
    # provider = "digitalocean"

    # By default, the provider will verify the TXT DNS challenge record before letting ACME verify.
    # If delayBeforeCheck is greater than zero, this check is delayed for the configured duration in seconds.
    # Useful if internal networks block external DNS queries.
    #
    # Optional
    # Default: 0
    #
    # delayBeforeCheck = 0

    # Use following DNS servers to resolve the FQDN authority.
    #
    # Optional
    # Default: empty
    #
    # resolvers = ["1.1.1.1:53", "8.8.8.8:53"]

    # Disable the DNS propagation checks before notifying ACME that the DNS challenge is ready.
    #
    # NOT RECOMMENDED:
    # Increase the risk of reaching Let's Encrypt's rate limits.
    #
    # Optional
    # Default: false
    #
    # disablePropagationCheck = true
