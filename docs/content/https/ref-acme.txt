# Enable ACME (Let's Encrypt): automatic SSL.

# Email address used for registration.
#
# Required
#
--certificatesresolvers.myresolver.acme.email=<EMAIL>

# File or key used for certificates storage.
#
# Required
#
--certificatesresolvers.myresolver.acme.storage=acme.json

# CA server to use.
# Uncomment the line to use Let's Encrypt's staging server,
# leave commented to go to prod.
#
# Optional
# Default: "https://acme-v02.api.letsencrypt.org/directory"
#
--certificatesresolvers.myresolver.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory

# The certificates' duration in hours.
# It defaults to 2160 (90 days) to follow Let's Encrypt certificates' duration.
#
# Optional
# Default: 2160
#
--certificatesresolvers.myresolver.acme.certificatesDuration=2160

# Timeout for a complete HTTP transaction with the ACME server.
#
# Optional
# Default: 2m
#
--certificatesresolvers.myresolver.acme.clientTimeout=2m

# Timeout for receiving the response headers when communicating with the ACME server.
#
# Optional
# Default: 30s
#
--certificatesresolvers.myresolver.acme.clientResponseHeaderTimeout=30s

# Preferred chain to use.
#
# If the CA offers multiple certificate chains, prefer the chain with an issuer matching this Subject Common Name.
# If no match, the default offered chain will be used.
#
# Optional
# Default: ""
#
--certificatesresolvers.myresolver.acme.preferredchain="ISRG Root X1"

# KeyType to use.
#
# Optional
# Default: "RSA4096"
#
# Available values : "EC256", "EC384", "RSA2048", "RSA4096", "RSA8192"
#
--certificatesresolvers.myresolver.acme.keytype=RSA4096

# Use a TLS-ALPN-01 ACME challenge.
#
# Optional (but recommended)
#
--certificatesresolvers.myresolver.acme.tlschallenge=true

# Use a HTTP-01 ACME challenge.
#
# Optional
#
--certificatesresolvers.myresolver.acme.httpchallenge=true

# EntryPoint to use for the HTTP-01 challenges.
#
# Required
#
--certificatesresolvers.myresolver.acme.httpchallenge.entrypoint=web

# Use a DNS-01 ACME challenge rather than HTTP-01 challenge.
# Note: mandatory for wildcard certificate generation.
#
# Optional
#
--certificatesresolvers.myresolver.acme.dnschallenge=true

# DNS provider used.
#
# Required
#
--certificatesresolvers.myresolver.acme.dnschallenge.provider=digitalocean

# By default, the provider will verify the TXT DNS challenge record before letting ACME verify.
# If delayBeforeCheck is greater than zero, this check is delayed for the configured duration in seconds.
# Useful if internal networks block external DNS queries.
#
# Optional
# Default: 0
#
--certificatesresolvers.myresolver.acme.dnschallenge.delaybeforecheck=0

# Use following DNS servers to resolve the FQDN authority.
#
# Optional
# Default: empty
#
--certificatesresolvers.myresolver.acme.dnschallenge.resolvers=1.1.1.1:53,8.8.8.8:53

# Disable the DNS propagation checks before notifying ACME that the DNS challenge is ready.
#
# NOT RECOMMENDED:
# Increase the risk of reaching Let's Encrypt's rate limits.
#
# Optional
# Default: false
#
--certificatesresolvers.myresolver.acme.dnschallenge.disablepropagationcheck=true
