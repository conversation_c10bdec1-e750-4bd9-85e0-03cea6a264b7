---
title: "Traefik Migration Documentation"
description: "Learn the steps needed to migrate to new Traefik Proxy v3 versions. Read the technical documentation."
---

# Migration: Steps needed between the versions

## v3.0 to v3.1

### Kubernetes Provider RBACs

Starting with v3.1, the Kubernetes Providers now use the [EndpointSlices API](https://kubernetes.io/docs/concepts/services-networking/endpoint-slices/) (Kubernetes >=v1.21) to discover service endpoint addresses.
It also brings NodePort load-balancing which requires Nodes resources lookup.

Therefore, in the corresponding RBACs (see [KubernetesIngress](../routing/providers/kubernetes-ingress.md#configuration-example), [KubernetesCRD](../reference/dynamic-configuration/kubernetes-crd.md#rbac), and [KubernetesGateway](../reference/dynamic-configuration/kubernetes-gateway-rbac.yml) provider RBACs):

- the `endpoints` right has to be removed and the following `endpointslices` right has to be added:

```yaml
  ... 
  - apiGroups:
      - discovery.k8s.io
    resources:
      - endpointslices
    verbs:
      - list
      - watch
  ...
```

- the `nodes` right has to be added:

```yaml
  ...
  - apiGroups:
      - ""
    resources:
      - nodes
    verbs:
      - get
      - list
      - watch
  ...
```

#### Gateway API: KubernetesGateway Provider

In v3.1, the KubernetesGateway Provider is no longer an experimental feature.
It can be enabled without the associated `experimental.kubernetesgateway` option, which is now deprecated.

??? example "An example of the experimental `kubernetesgateway` option"

    ```yaml tab="File (YAML)"
    experimental:
      kubernetesgateway: true
    ```

    ```toml tab="File (TOML)"
    [experimental]
        kubernetesgateway=true
    ```

    ```bash tab="CLI"
    --experimental.kubernetesgateway=true
    ```

##### Remediation

The `kubernetesgateway` option should be removed from the experimental section of the static configuration.
To configure `kubernetesgateway`, please check out the [KubernetesGateway Provider documentation](../providers/kubernetes-gateway.md).

## v3.1.0 to v3.1.1

### IngressClass Lookup

The Kubernetes Ingress provider option `disableIngressClassLookup` has been deprecated in v3.1.1, and will be removed in the next major version.
Please use the `disableClusterScopeResources` option instead to avoid cluster scope resources discovery (IngressClass, Nodes).

## v3.1 to v3.2

### Kubernetes CRD Provider

Starting with v3.2, the CRDs has been updated on [TraefikService](../../routing/services#mirroring-service) (PR [#11032](https://github.com/traefik/traefik/pull/11032)), on [RateLimit](../../middlewares/http/ratelimit) & [InFlightReq](../../middlewares/http/inflightreq) middlewares (PR [#9747](https://github.com/traefik/traefik/pull/9747)) and on [Compress](../../middlewares/http/compress) middleware (PR [#10943](https://github.com/traefik/traefik/pull/10943)).

This update adds only new optional fields.
CRDs can be updated with this command:

```shell
kubectl apply -f https://raw.githubusercontent.com/traefik/traefik/v3.3/docs/content/reference/dynamic-configuration/kubernetes-crd-definition-v1.yml
```

### Kubernetes Gateway Provider Standard Channel

Starting with v3.2, the Kubernetes Gateway Provider now supports [GRPCRoute](https://gateway-api.sigs.k8s.io/api-types/grpcroute/).

Therefore, in the corresponding RBACs (see [KubernetesGateway](../reference/dynamic-configuration/kubernetes-gateway-rbac.yml) provider RBACs),
the `grcroutes` and `grpcroutes/status` rights have to be added.

```yaml
  ...
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - grpcroutes
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - grpcroutes/status
    verbs:
      - update
  ...
```

### Kubernetes Gateway Provider Experimental Channel

!!! warning "Breaking changes"

    Because of a breaking change introduced in Kubernetes Gateway [v1.2.0-rc1](https://github.com/kubernetes-sigs/gateway-api/releases/tag/v1.2.0-rc1),
    Traefik v3.3 only supports Kubernetes Gateway v1.2.x when experimental channel features are enabled.

Starting with v3.2, the Kubernetes Gateway Provider now supports [BackendTLSPolicy](https://gateway-api.sigs.k8s.io/api-types/backendtlspolicy/).

Therefore, in the corresponding RBACs (see [KubernetesGateway](../reference/dynamic-configuration/kubernetes-gateway-rbac.yml) provider RBACs),
the `backendtlspolicies` and `backendtlspolicies/status` rights have to be added.

```yaml
  ...
  - apiGroups:
      - ""
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - backendtlspolicies
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - backendtlspolicies/status
    verbs:
      - update
  ...
```

## v3.2.1

### X-Forwarded-Prefix

In `v3.2.1`, the `X-Forwarded-Prefix` header is now handled like the other `X-Forwarded-*` headers: Traefik removes it when it's sent from an untrusted source.
Please refer to the Forwarded headers [documentation](../routing/entrypoints.md#forwarded-headers) for more details.

## v3.2.2

### Swarm Provider

In `v3.2.2`, the `traefik.docker.network` and `traefik.docker.lbswarm` labels have been deprecated,
please use the `traefik.swarm.network` and `traefik.swarm.lbswarm` labels instead.

## v3.2 to v3.3

### ACME DNS Certificate Resolver

In `v3.3`, the `acme.dnsChallenge.delaybeforecheck` and `acme.dnsChallenge.disablepropagationcheck` options of the ACME certificate resolver are deprecated, 
please use respectively `acme.dnsChallenge.propagation.delayBeforeChecks` and `acme.dnsChallenge.propagation.disableChecks` options instead.

### Tracing Global Attributes

In `v3.3`, the `tracing.globalAttributes` option has been deprecated, please use the `tracing.resourceAttributes` option instead.
The `tracing.globalAttributes` option is misleading as its name does not reflect the operation of adding resource attributes to be sent to the collector,
and will be removed in the next major version.

## v3.3.4

### OpenTelemetry Request Duration metric

In `v3.3.4`, the OpenTelemetry Request Duration metric (named `traefik_(entrypoint|router|service)_request_duration_seconds`) unit has been changed from milliseconds to seconds.
To be consistent with the naming and other metrics providers, the metric now reports the duration in seconds.

## v3.3.5

### Compress Middleware

In `v3.3.5`, the compress middleware `encodings` option default value is now `gzip, br, zstd`. 
This change helps the algorithm selection to favor the `gzip` algorithm over the other algorithms.

It impacts requests that do not specify their preferred algorithm,
or has no order preference, in the `Accept-Encoding` header.

## v3.3.6

### Request Path Sanitization

Since `v3.3.6`, the incoming request path is now cleaned before being used to match the router rules and sent to the backends.
Any `/../`, `/./` or duplicate slash segments in the request path is interpreted and/or collapsed.

If you want to disable this behavior, you can set the [`sanitizePath` option](../reference/install-configuration/entrypoints.md#sanitizepath) to `false` in the entryPoint HTTP configuration.
This can be useful when dealing with legacy clients that are not url-encoding data in the request path.
For example, as base64 uses the “/” character internally,
if it's not url encoded,
it can lead to unsafe routing when the `sanitizePath` option is set to `false`.

!!! warning "Security"

    Setting the `sanitizePath` option to `false` is not safe.
    Ensure every request is properly url encoded instead.

## v3.3 to v3.4

### Kubernetes CRD Provider

#### Load-Balancing

In `v3.4`, the HTTP service definition has been updated.
The strategy field now supports two new values: `wrr` and `p2c` (please refer to the [HTTP Services Load Balancing documentation](../../routing/services/#load-balancing-strategy) for more details).

CRDs can be updated with this command:

```shell
kubectl apply -f https://raw.githubusercontent.com/traefik/traefik/v3.4/docs/content/reference/dynamic-configuration/kubernetes-crd-definition-v1.yml
```

Please note that the `RoundRobin` strategy value is now deprecated, but still supported and equivalent to `wrr`, and will be removed in the next major release.

#### ServersTransport CA Certificate

In `v3.4`, a new `rootCAs` option has been added to the `ServersTransport` and `ServersTransportTCP` CRDs.
It allows the configuration of CA certificates from both `ConfigMaps` and `Secrets`,
and replaces the `rootCAsSecrets` option, as shown below:

CRDs can be updated with this command:

```shell
kubectl apply -f https://raw.githubusercontent.com/traefik/traefik/v3.4/docs/content/reference/dynamic-configuration/kubernetes-crd-definition-v1.yml
```

RBACs need to be updated with this command:

```shell
kubectl apply -f https://raw.githubusercontent.com/traefik/traefik/v3.4/docs/content/reference/dynamic-configuration/kubernetes-crd-rbac.yml
```

```yaml
---
apiVersion: traefik.io/v1alpha1
kind: ServersTransport
metadata:
  name: foo
  namespace: bar
spec:
  rootCAs:
    - configMap: ca-config-map
    - secret: ca-secret
      
---      
apiVersion: traefik.io/v1alpha1
kind: ServersTransportTCP
metadata:
  name: foo
  namespace: bar
spec:
  rootCAs:
    - configMap: ca-config-map
    - secret: ca-secret
```

The `rootCAsSecrets` option, which allows only `Secrets` references,
is still supported, but is now deprecated,
and will be removed in the next major release.

### Rule Syntax

In `v3.4.0`, the `core.defaultRuleSyntax` static configuration option and the `ruleSyntax` router option have been deprecated,
and will be removed in the next major version.

This `core.defaultRuleSyntax` option was used to switch between the v2 and v3 syntax for the router's rules,
and to help with the migration from v2 to v3.

The `ruleSyntax` router's option was used to override the default rule syntax for a specific router.

In preparation for the next major release, please remove any use of these two options and use the v3 syntax for writing the router's rules.

## v3.4.1

### Request Path Normalization

Since `v3.4.1`, the request path is now normalized by decoding unreserved characters in the request path,
and also uppercasing the percent-encoded characters.
This follows [RFC 3986 percent-encoding normalization](https://datatracker.ietf.org/doc/html/rfc3986#section-6.2.2.2),
and [RFC 3986 case normalization](https://datatracker.ietf.org/doc/html/rfc3986#section-6.2.2.1).

The normalization happens before the request path is sanitized,
and cannot be disabled.
This notably helps with encoded dots characters (which are unreserved characters) to be sanitized properly.

### Routing Path

Since `v3.4.1`, the reserved characters [(as per RFC 3986)](https://datatracker.ietf.org/doc/html/rfc3986#section-2.2) are kept encoded in the request path when matching the router rules.
Those characters, when decoded, change the meaning of the request path for routing purposes,
and Traefik now keeps them encoded to avoid any ambiguity.

### Request Path Matching Examples

| Request Path      | Router Rule            | Traefik v3.4.0 | Traefik v3.4.1 |
|-------------------|------------------------|----------------|----------------|
| `/foo%2Fbar`      | PathPrefix(`/foo/bar`) | Match          | No match       |
| `/foo/../bar`     | PathPrefix(`/foo`)     | No match       | No match       |
| `/foo/../bar`     | PathPrefix(`/bar`)     | Match          | Match          |
| `/foo/%2E%2E/bar` | PathPrefix(`/foo`)     | Match          | No match       |
| `/foo/%2E%2E/bar` | PathPrefix(`/bar`)     | No match       | Match          |
