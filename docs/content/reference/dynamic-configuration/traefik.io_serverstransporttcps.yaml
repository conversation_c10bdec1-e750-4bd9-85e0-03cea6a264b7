---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: serverstransporttcps.traefik.io
spec:
  group: traefik.io
  names:
    kind: ServersTransportTCP
    listKind: ServersTransportTCPList
    plural: serverstransporttcps
    singular: serverstransporttcp
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          ServersTransportTCP is the CRD implementation of a TCPServersTransport.
          If no tcpServersTransport is specified, a default one named default@internal will be used.
          The default@internal tcpServersTransport can be configured in the static configuration.
          More info: https://doc.traefik.io/traefik/v3.5/routing/services/#serverstransport_3
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ServersTransportTCPSpec defines the desired state of a ServersTransportTCP.
            properties:
              dialKeepAlive:
                anyOf:
                - type: integer
                - type: string
                description: DialKeepAlive is the interval between keep-alive probes
                  for an active network connection. If zero, keep-alive probes are
                  sent with a default value (currently 15 seconds), if supported by
                  the protocol and operating system. Network protocols or operating
                  systems that do not support keep-alives ignore this field. If negative,
                  keep-alive probes are disabled.
                pattern: ^([0-9]+(ns|us|µs|ms|s|m|h)?)+$
                x-kubernetes-int-or-string: true
              dialTimeout:
                anyOf:
                - type: integer
                - type: string
                description: DialTimeout is the amount of time to wait until a connection
                  to a backend server can be established.
                pattern: ^([0-9]+(ns|us|µs|ms|s|m|h)?)+$
                x-kubernetes-int-or-string: true
              terminationDelay:
                anyOf:
                - type: integer
                - type: string
                description: TerminationDelay defines the delay to wait before fully
                  terminating the connection, after one connected peer has closed
                  its writing capability.
                pattern: ^([0-9]+(ns|us|µs|ms|s|m|h)?)+$
                x-kubernetes-int-or-string: true
              tls:
                description: TLS defines the TLS configuration
                properties:
                  certificatesSecrets:
                    description: CertificatesSecrets defines a list of secret storing
                      client certificates for mTLS.
                    items:
                      type: string
                    type: array
                  insecureSkipVerify:
                    description: InsecureSkipVerify disables TLS certificate verification.
                    type: boolean
                  peerCertURI:
                    description: |-
                      MaxIdleConnsPerHost controls the maximum idle (keep-alive) to keep per-host.
                      PeerCertURI defines the peer cert URI used to match against SAN URI during the peer certificate verification.
                    type: string
                  rootCAs:
                    description: RootCAs defines a list of CA certificate Secrets
                      or ConfigMaps used to validate server certificates.
                    items:
                      description: |-
                        RootCA defines a reference to a Secret or a ConfigMap that holds a CA certificate.
                        If both a Secret and a ConfigMap reference are defined, the Secret reference takes precedence.
                      properties:
                        configMap:
                          description: |-
                            ConfigMap defines the name of a ConfigMap that holds a CA certificate.
                            The referenced ConfigMap must contain a certificate under either a tls.ca or a ca.crt key.
                          type: string
                        secret:
                          description: |-
                            Secret defines the name of a Secret that holds a CA certificate.
                            The referenced Secret must contain a certificate under either a tls.ca or a ca.crt key.
                          type: string
                      type: object
                      x-kubernetes-validations:
                      - message: RootCA cannot have both Secret and ConfigMap defined.
                        rule: '!has(self.secret) || !has(self.configMap)'
                    type: array
                  rootCAsSecrets:
                    description: |-
                      RootCAsSecrets defines a list of CA secret used to validate self-signed certificate.
                      Deprecated: RootCAsSecrets is deprecated, please use the RootCAs option instead.
                    items:
                      type: string
                    type: array
                  serverName:
                    description: ServerName defines the server name used to contact
                      the server.
                    type: string
                  spiffe:
                    description: Spiffe defines the SPIFFE configuration.
                    properties:
                      ids:
                        description: IDs defines the allowed SPIFFE IDs (takes precedence
                          over the SPIFFE TrustDomain).
                        items:
                          type: string
                        type: array
                      trustDomain:
                        description: TrustDomain defines the allowed SPIFFE trust
                          domain.
                        type: string
                    type: object
                type: object
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
