---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: middlewaretcps.traefik.io
spec:
  group: traefik.io
  names:
    kind: MiddlewareTCP
    listKind: MiddlewareTCPList
    plural: middlewaretcps
    singular: middlewaretcp
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          MiddlewareTCP is the CRD implementation of a Traefik TCP middleware.
          More info: https://doc.traefik.io/traefik/v3.5/middlewares/overview/
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: MiddlewareTCPSpec defines the desired state of a MiddlewareTCP.
            properties:
              inFlightConn:
                description: InFlightConn defines the InFlightConn middleware configuration.
                properties:
                  amount:
                    description: |-
                      Amount defines the maximum amount of allowed simultaneous connections.
                      The middleware closes the connection if there are already amount connections opened.
                    format: int64
                    minimum: 0
                    type: integer
                type: object
              ipAllowList:
                description: |-
                  IPAllowList defines the IPAllowList middleware configuration.
                  This middleware accepts/refuses connections based on the client IP.
                  More info: https://doc.traefik.io/traefik/v3.5/middlewares/tcp/ipallowlist/
                properties:
                  sourceRange:
                    description: SourceRange defines the allowed IPs (or ranges of
                      allowed IPs by using CIDR notation).
                    items:
                      type: string
                    type: array
                type: object
              ipWhiteList:
                description: |-
                  IPWhiteList defines the IPWhiteList middleware configuration.
                  This middleware accepts/refuses connections based on the client IP.
                  Deprecated: please use IPAllowList instead.
                  More info: https://doc.traefik.io/traefik/v3.5/middlewares/tcp/ipwhitelist/
                properties:
                  sourceRange:
                    description: SourceRange defines the allowed IPs (or ranges of
                      allowed IPs by using CIDR notation).
                    items:
                      type: string
                    type: array
                type: object
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
