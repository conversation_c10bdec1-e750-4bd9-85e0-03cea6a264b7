<!--
CODE GENERATED AUTOMATICALLY
THIS FILE MUST NOT BE EDITED BY HAND
-->

| Key (Path) | Value |
|------------|-------|
| `traefik/http/middlewares/Middleware01/addPrefix/prefix` | `foobar` |
| `traefik/http/middlewares/Middleware02/basicAuth/headerField` | `foobar` |
| `traefik/http/middlewares/Middleware02/basicAuth/realm` | `foobar` |
| `traefik/http/middlewares/Middleware02/basicAuth/removeHeader` | `true` |
| `traefik/http/middlewares/Middleware02/basicAuth/users/0` | `foobar` |
| `traefik/http/middlewares/Middleware02/basicAuth/users/1` | `foobar` |
| `traefik/http/middlewares/Middleware02/basicAuth/usersFile` | `foobar` |
| `traefik/http/middlewares/Middleware03/buffering/maxRequestBodyBytes` | `42` |
| `traefik/http/middlewares/Middleware03/buffering/maxResponseBodyBytes` | `42` |
| `traefik/http/middlewares/Middleware03/buffering/memRequestBodyBytes` | `42` |
| `traefik/http/middlewares/Middleware03/buffering/memResponseBodyBytes` | `42` |
| `traefik/http/middlewares/Middleware03/buffering/retryExpression` | `foobar` |
| `traefik/http/middlewares/Middleware04/chain/middlewares/0` | `foobar` |
| `traefik/http/middlewares/Middleware04/chain/middlewares/1` | `foobar` |
| `traefik/http/middlewares/Middleware05/circuitBreaker/checkPeriod` | `42s` |
| `traefik/http/middlewares/Middleware05/circuitBreaker/expression` | `foobar` |
| `traefik/http/middlewares/Middleware05/circuitBreaker/fallbackDuration` | `42s` |
| `traefik/http/middlewares/Middleware05/circuitBreaker/recoveryDuration` | `42s` |
| `traefik/http/middlewares/Middleware05/circuitBreaker/responseCode` | `42` |
| `traefik/http/middlewares/Middleware06/compress/defaultEncoding` | `foobar` |
| `traefik/http/middlewares/Middleware06/compress/encodings/0` | `foobar` |
| `traefik/http/middlewares/Middleware06/compress/encodings/1` | `foobar` |
| `traefik/http/middlewares/Middleware06/compress/excludedContentTypes/0` | `foobar` |
| `traefik/http/middlewares/Middleware06/compress/excludedContentTypes/1` | `foobar` |
| `traefik/http/middlewares/Middleware06/compress/includedContentTypes/0` | `foobar` |
| `traefik/http/middlewares/Middleware06/compress/includedContentTypes/1` | `foobar` |
| `traefik/http/middlewares/Middleware06/compress/minResponseBodyBytes` | `42` |
| `traefik/http/middlewares/Middleware07/contentType/autoDetect` | `true` |
| `traefik/http/middlewares/Middleware08/digestAuth/headerField` | `foobar` |
| `traefik/http/middlewares/Middleware08/digestAuth/realm` | `foobar` |
| `traefik/http/middlewares/Middleware08/digestAuth/removeHeader` | `true` |
| `traefik/http/middlewares/Middleware08/digestAuth/users/0` | `foobar` |
| `traefik/http/middlewares/Middleware08/digestAuth/users/1` | `foobar` |
| `traefik/http/middlewares/Middleware08/digestAuth/usersFile` | `foobar` |
| `traefik/http/middlewares/Middleware09/errors/query` | `foobar` |
| `traefik/http/middlewares/Middleware09/errors/service` | `foobar` |
| `traefik/http/middlewares/Middleware09/errors/status/0` | `foobar` |
| `traefik/http/middlewares/Middleware09/errors/status/1` | `foobar` |
| `traefik/http/middlewares/Middleware09/errors/statusRewrites/name0` | `42` |
| `traefik/http/middlewares/Middleware09/errors/statusRewrites/name1` | `42` |
| `traefik/http/middlewares/Middleware10/forwardAuth/addAuthCookiesToResponse/0` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/addAuthCookiesToResponse/1` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/address` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/authRequestHeaders/0` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/authRequestHeaders/1` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/authResponseHeaders/0` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/authResponseHeaders/1` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/authResponseHeadersRegex` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/forwardBody` | `true` |
| `traefik/http/middlewares/Middleware10/forwardAuth/headerField` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/maxBodySize` | `42` |
| `traefik/http/middlewares/Middleware10/forwardAuth/preserveLocationHeader` | `true` |
| `traefik/http/middlewares/Middleware10/forwardAuth/preserveRequestMethod` | `true` |
| `traefik/http/middlewares/Middleware10/forwardAuth/tls/ca` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/tls/caOptional` | `true` |
| `traefik/http/middlewares/Middleware10/forwardAuth/tls/cert` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/tls/insecureSkipVerify` | `true` |
| `traefik/http/middlewares/Middleware10/forwardAuth/tls/key` | `foobar` |
| `traefik/http/middlewares/Middleware10/forwardAuth/trustForwardHeader` | `true` |
| `traefik/http/middlewares/Middleware11/grpcWeb/allowOrigins/0` | `foobar` |
| `traefik/http/middlewares/Middleware11/grpcWeb/allowOrigins/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowCredentials` | `true` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowHeaders/0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowHeaders/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowMethods/0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowMethods/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowOriginList/0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowOriginList/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowOriginListRegex/0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlAllowOriginListRegex/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlExposeHeaders/0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlExposeHeaders/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/accessControlMaxAge` | `42` |
| `traefik/http/middlewares/Middleware12/headers/addVaryHeader` | `true` |
| `traefik/http/middlewares/Middleware12/headers/allowedHosts/0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/allowedHosts/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/browserXssFilter` | `true` |
| `traefik/http/middlewares/Middleware12/headers/contentSecurityPolicy` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/contentSecurityPolicyReportOnly` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/contentTypeNosniff` | `true` |
| `traefik/http/middlewares/Middleware12/headers/customBrowserXSSValue` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/customFrameOptionsValue` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/customRequestHeaders/name0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/customRequestHeaders/name1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/customResponseHeaders/name0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/customResponseHeaders/name1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/featurePolicy` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/forceSTSHeader` | `true` |
| `traefik/http/middlewares/Middleware12/headers/frameDeny` | `true` |
| `traefik/http/middlewares/Middleware12/headers/hostsProxyHeaders/0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/hostsProxyHeaders/1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/isDevelopment` | `true` |
| `traefik/http/middlewares/Middleware12/headers/permissionsPolicy` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/publicKey` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/referrerPolicy` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/sslForceHost` | `true` |
| `traefik/http/middlewares/Middleware12/headers/sslHost` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/sslProxyHeaders/name0` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/sslProxyHeaders/name1` | `foobar` |
| `traefik/http/middlewares/Middleware12/headers/sslRedirect` | `true` |
| `traefik/http/middlewares/Middleware12/headers/sslTemporaryRedirect` | `true` |
| `traefik/http/middlewares/Middleware12/headers/stsIncludeSubdomains` | `true` |
| `traefik/http/middlewares/Middleware12/headers/stsPreload` | `true` |
| `traefik/http/middlewares/Middleware12/headers/stsSeconds` | `42` |
| `traefik/http/middlewares/Middleware13/ipAllowList/ipStrategy/depth` | `42` |
| `traefik/http/middlewares/Middleware13/ipAllowList/ipStrategy/excludedIPs/0` | `foobar` |
| `traefik/http/middlewares/Middleware13/ipAllowList/ipStrategy/excludedIPs/1` | `foobar` |
| `traefik/http/middlewares/Middleware13/ipAllowList/ipStrategy/ipv6Subnet` | `42` |
| `traefik/http/middlewares/Middleware13/ipAllowList/rejectStatusCode` | `42` |
| `traefik/http/middlewares/Middleware13/ipAllowList/sourceRange/0` | `foobar` |
| `traefik/http/middlewares/Middleware13/ipAllowList/sourceRange/1` | `foobar` |
| `traefik/http/middlewares/Middleware14/ipWhiteList/ipStrategy/depth` | `42` |
| `traefik/http/middlewares/Middleware14/ipWhiteList/ipStrategy/excludedIPs/0` | `foobar` |
| `traefik/http/middlewares/Middleware14/ipWhiteList/ipStrategy/excludedIPs/1` | `foobar` |
| `traefik/http/middlewares/Middleware14/ipWhiteList/ipStrategy/ipv6Subnet` | `42` |
| `traefik/http/middlewares/Middleware14/ipWhiteList/sourceRange/0` | `foobar` |
| `traefik/http/middlewares/Middleware14/ipWhiteList/sourceRange/1` | `foobar` |
| `traefik/http/middlewares/Middleware15/inFlightReq/amount` | `42` |
| `traefik/http/middlewares/Middleware15/inFlightReq/sourceCriterion/ipStrategy/depth` | `42` |
| `traefik/http/middlewares/Middleware15/inFlightReq/sourceCriterion/ipStrategy/excludedIPs/0` | `foobar` |
| `traefik/http/middlewares/Middleware15/inFlightReq/sourceCriterion/ipStrategy/excludedIPs/1` | `foobar` |
| `traefik/http/middlewares/Middleware15/inFlightReq/sourceCriterion/ipStrategy/ipv6Subnet` | `42` |
| `traefik/http/middlewares/Middleware15/inFlightReq/sourceCriterion/requestHeaderName` | `foobar` |
| `traefik/http/middlewares/Middleware15/inFlightReq/sourceCriterion/requestHost` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/issuer/commonName` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/issuer/country` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/issuer/domainComponent` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/issuer/locality` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/issuer/organization` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/issuer/province` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/issuer/serialNumber` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/notAfter` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/notBefore` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/sans` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/serialNumber` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/commonName` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/country` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/domainComponent` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/locality` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/organization` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/organizationalUnit` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/province` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/info/subject/serialNumber` | `true` |
| `traefik/http/middlewares/Middleware16/passTLSClientCert/pem` | `true` |
| `traefik/http/middlewares/Middleware17/plugin/PluginConf0/name0` | `foobar` |
| `traefik/http/middlewares/Middleware17/plugin/PluginConf0/name1` | `foobar` |
| `traefik/http/middlewares/Middleware17/plugin/PluginConf1/name0` | `foobar` |
| `traefik/http/middlewares/Middleware17/plugin/PluginConf1/name1` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/average` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/burst` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/period` | `42s` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/db` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/dialTimeout` | `42s` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/endpoints/0` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/endpoints/1` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/maxActiveConns` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/minIdleConns` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/password` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/poolSize` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/readTimeout` | `42s` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/tls/ca` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/tls/cert` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/tls/insecureSkipVerify` | `true` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/tls/key` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/username` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/redis/writeTimeout` | `42s` |
| `traefik/http/middlewares/Middleware18/rateLimit/sourceCriterion/ipStrategy/depth` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/sourceCriterion/ipStrategy/excludedIPs/0` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/sourceCriterion/ipStrategy/excludedIPs/1` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/sourceCriterion/ipStrategy/ipv6Subnet` | `42` |
| `traefik/http/middlewares/Middleware18/rateLimit/sourceCriterion/requestHeaderName` | `foobar` |
| `traefik/http/middlewares/Middleware18/rateLimit/sourceCriterion/requestHost` | `true` |
| `traefik/http/middlewares/Middleware19/redirectRegex/permanent` | `true` |
| `traefik/http/middlewares/Middleware19/redirectRegex/regex` | `foobar` |
| `traefik/http/middlewares/Middleware19/redirectRegex/replacement` | `foobar` |
| `traefik/http/middlewares/Middleware20/redirectScheme/permanent` | `true` |
| `traefik/http/middlewares/Middleware20/redirectScheme/port` | `foobar` |
| `traefik/http/middlewares/Middleware20/redirectScheme/scheme` | `foobar` |
| `traefik/http/middlewares/Middleware21/replacePath/path` | `foobar` |
| `traefik/http/middlewares/Middleware22/replacePathRegex/regex` | `foobar` |
| `traefik/http/middlewares/Middleware22/replacePathRegex/replacement` | `foobar` |
| `traefik/http/middlewares/Middleware23/retry/attempts` | `42` |
| `traefik/http/middlewares/Middleware23/retry/initialInterval` | `42s` |
| `traefik/http/middlewares/Middleware24/stripPrefix/forceSlash` | `true` |
| `traefik/http/middlewares/Middleware24/stripPrefix/prefixes/0` | `foobar` |
| `traefik/http/middlewares/Middleware24/stripPrefix/prefixes/1` | `foobar` |
| `traefik/http/middlewares/Middleware25/stripPrefixRegex/regex/0` | `foobar` |
| `traefik/http/middlewares/Middleware25/stripPrefixRegex/regex/1` | `foobar` |
| `traefik/http/routers/Router0/entryPoints/0` | `foobar` |
| `traefik/http/routers/Router0/entryPoints/1` | `foobar` |
| `traefik/http/routers/Router0/middlewares/0` | `foobar` |
| `traefik/http/routers/Router0/middlewares/1` | `foobar` |
| `traefik/http/routers/Router0/observability/accessLogs` | `true` |
| `traefik/http/routers/Router0/observability/metrics` | `true` |
| `traefik/http/routers/Router0/observability/tracing` | `true` |
| `traefik/http/routers/Router0/priority` | `42` |
| `traefik/http/routers/Router0/rule` | `foobar` |
| `traefik/http/routers/Router0/ruleSyntax` | `foobar` |
| `traefik/http/routers/Router0/service` | `foobar` |
| `traefik/http/routers/Router0/tls/certResolver` | `foobar` |
| `traefik/http/routers/Router0/tls/domains/0/main` | `foobar` |
| `traefik/http/routers/Router0/tls/domains/0/sans/0` | `foobar` |
| `traefik/http/routers/Router0/tls/domains/0/sans/1` | `foobar` |
| `traefik/http/routers/Router0/tls/domains/1/main` | `foobar` |
| `traefik/http/routers/Router0/tls/domains/1/sans/0` | `foobar` |
| `traefik/http/routers/Router0/tls/domains/1/sans/1` | `foobar` |
| `traefik/http/routers/Router0/tls/options` | `foobar` |
| `traefik/http/routers/Router1/entryPoints/0` | `foobar` |
| `traefik/http/routers/Router1/entryPoints/1` | `foobar` |
| `traefik/http/routers/Router1/middlewares/0` | `foobar` |
| `traefik/http/routers/Router1/middlewares/1` | `foobar` |
| `traefik/http/routers/Router1/observability/accessLogs` | `true` |
| `traefik/http/routers/Router1/observability/metrics` | `true` |
| `traefik/http/routers/Router1/observability/tracing` | `true` |
| `traefik/http/routers/Router1/priority` | `42` |
| `traefik/http/routers/Router1/rule` | `foobar` |
| `traefik/http/routers/Router1/ruleSyntax` | `foobar` |
| `traefik/http/routers/Router1/service` | `foobar` |
| `traefik/http/routers/Router1/tls/certResolver` | `foobar` |
| `traefik/http/routers/Router1/tls/domains/0/main` | `foobar` |
| `traefik/http/routers/Router1/tls/domains/0/sans/0` | `foobar` |
| `traefik/http/routers/Router1/tls/domains/0/sans/1` | `foobar` |
| `traefik/http/routers/Router1/tls/domains/1/main` | `foobar` |
| `traefik/http/routers/Router1/tls/domains/1/sans/0` | `foobar` |
| `traefik/http/routers/Router1/tls/domains/1/sans/1` | `foobar` |
| `traefik/http/routers/Router1/tls/options` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/certificates/0/certFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/certificates/0/keyFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/certificates/1/certFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/certificates/1/keyFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/disableHTTP2` | `true` |
| `traefik/http/serversTransports/ServersTransport0/forwardingTimeouts/dialTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport0/forwardingTimeouts/idleConnTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport0/forwardingTimeouts/pingTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport0/forwardingTimeouts/readIdleTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport0/forwardingTimeouts/responseHeaderTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport0/insecureSkipVerify` | `true` |
| `traefik/http/serversTransports/ServersTransport0/maxIdleConnsPerHost` | `42` |
| `traefik/http/serversTransports/ServersTransport0/peerCertURI` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/rootCAs/0` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/rootCAs/1` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/serverName` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/spiffe/ids/0` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/spiffe/ids/1` | `foobar` |
| `traefik/http/serversTransports/ServersTransport0/spiffe/trustDomain` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/certificates/0/certFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/certificates/0/keyFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/certificates/1/certFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/certificates/1/keyFile` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/disableHTTP2` | `true` |
| `traefik/http/serversTransports/ServersTransport1/forwardingTimeouts/dialTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport1/forwardingTimeouts/idleConnTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport1/forwardingTimeouts/pingTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport1/forwardingTimeouts/readIdleTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport1/forwardingTimeouts/responseHeaderTimeout` | `42s` |
| `traefik/http/serversTransports/ServersTransport1/insecureSkipVerify` | `true` |
| `traefik/http/serversTransports/ServersTransport1/maxIdleConnsPerHost` | `42` |
| `traefik/http/serversTransports/ServersTransport1/peerCertURI` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/rootCAs/0` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/rootCAs/1` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/serverName` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/spiffe/ids/0` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/spiffe/ids/1` | `foobar` |
| `traefik/http/serversTransports/ServersTransport1/spiffe/trustDomain` | `foobar` |
| `traefik/http/services/Service01/failover/fallback` | `foobar` |
| `traefik/http/services/Service01/failover/healthCheck` | `` |
| `traefik/http/services/Service01/failover/service` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/followRedirects` | `true` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/headers/name0` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/headers/name1` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/hostname` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/interval` | `42s` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/method` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/mode` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/path` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/port` | `42` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/scheme` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/status` | `42` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/timeout` | `42s` |
| `traefik/http/services/Service02/loadBalancer/healthCheck/unhealthyInterval` | `42s` |
| `traefik/http/services/Service02/loadBalancer/passHostHeader` | `true` |
| `traefik/http/services/Service02/loadBalancer/responseForwarding/flushInterval` | `42s` |
| `traefik/http/services/Service02/loadBalancer/servers/0/preservePath` | `true` |
| `traefik/http/services/Service02/loadBalancer/servers/0/url` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/servers/0/weight` | `42` |
| `traefik/http/services/Service02/loadBalancer/servers/1/preservePath` | `true` |
| `traefik/http/services/Service02/loadBalancer/servers/1/url` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/servers/1/weight` | `42` |
| `traefik/http/services/Service02/loadBalancer/serversTransport` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/sticky/cookie/domain` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/sticky/cookie/httpOnly` | `true` |
| `traefik/http/services/Service02/loadBalancer/sticky/cookie/maxAge` | `42` |
| `traefik/http/services/Service02/loadBalancer/sticky/cookie/name` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/sticky/cookie/path` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/sticky/cookie/sameSite` | `foobar` |
| `traefik/http/services/Service02/loadBalancer/sticky/cookie/secure` | `true` |
| `traefik/http/services/Service02/loadBalancer/strategy` | `foobar` |
| `traefik/http/services/Service03/mirroring/healthCheck` | `` |
| `traefik/http/services/Service03/mirroring/maxBodySize` | `42` |
| `traefik/http/services/Service03/mirroring/mirrorBody` | `true` |
| `traefik/http/services/Service03/mirroring/mirrors/0/name` | `foobar` |
| `traefik/http/services/Service03/mirroring/mirrors/0/percent` | `42` |
| `traefik/http/services/Service03/mirroring/mirrors/1/name` | `foobar` |
| `traefik/http/services/Service03/mirroring/mirrors/1/percent` | `42` |
| `traefik/http/services/Service03/mirroring/service` | `foobar` |
| `traefik/http/services/Service04/weighted/healthCheck` | `` |
| `traefik/http/services/Service04/weighted/services/0/name` | `foobar` |
| `traefik/http/services/Service04/weighted/services/0/weight` | `42` |
| `traefik/http/services/Service04/weighted/services/1/name` | `foobar` |
| `traefik/http/services/Service04/weighted/services/1/weight` | `42` |
| `traefik/http/services/Service04/weighted/sticky/cookie/domain` | `foobar` |
| `traefik/http/services/Service04/weighted/sticky/cookie/httpOnly` | `true` |
| `traefik/http/services/Service04/weighted/sticky/cookie/maxAge` | `42` |
| `traefik/http/services/Service04/weighted/sticky/cookie/name` | `foobar` |
| `traefik/http/services/Service04/weighted/sticky/cookie/path` | `foobar` |
| `traefik/http/services/Service04/weighted/sticky/cookie/sameSite` | `foobar` |
| `traefik/http/services/Service04/weighted/sticky/cookie/secure` | `true` |
| `traefik/tcp/middlewares/TCPMiddleware01/ipAllowList/sourceRange/0` | `foobar` |
| `traefik/tcp/middlewares/TCPMiddleware01/ipAllowList/sourceRange/1` | `foobar` |
| `traefik/tcp/middlewares/TCPMiddleware02/ipWhiteList/sourceRange/0` | `foobar` |
| `traefik/tcp/middlewares/TCPMiddleware02/ipWhiteList/sourceRange/1` | `foobar` |
| `traefik/tcp/middlewares/TCPMiddleware03/inFlightConn/amount` | `42` |
| `traefik/tcp/routers/TCPRouter0/entryPoints/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/entryPoints/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/middlewares/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/middlewares/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/priority` | `42` |
| `traefik/tcp/routers/TCPRouter0/rule` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/ruleSyntax` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/service` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/certResolver` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/domains/0/main` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/domains/0/sans/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/domains/0/sans/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/domains/1/main` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/domains/1/sans/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/domains/1/sans/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/options` | `foobar` |
| `traefik/tcp/routers/TCPRouter0/tls/passthrough` | `true` |
| `traefik/tcp/routers/TCPRouter1/entryPoints/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/entryPoints/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/middlewares/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/middlewares/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/priority` | `42` |
| `traefik/tcp/routers/TCPRouter1/rule` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/ruleSyntax` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/service` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/certResolver` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/domains/0/main` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/domains/0/sans/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/domains/0/sans/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/domains/1/main` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/domains/1/sans/0` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/domains/1/sans/1` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/options` | `foobar` |
| `traefik/tcp/routers/TCPRouter1/tls/passthrough` | `true` |
| `traefik/tcp/serversTransports/TCPServersTransport0/dialKeepAlive` | `42s` |
| `traefik/tcp/serversTransports/TCPServersTransport0/dialTimeout` | `42s` |
| `traefik/tcp/serversTransports/TCPServersTransport0/terminationDelay` | `42s` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/certificates/0/certFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/certificates/0/keyFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/certificates/1/certFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/certificates/1/keyFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/insecureSkipVerify` | `true` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/peerCertURI` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/rootCAs/0` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/rootCAs/1` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/serverName` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/spiffe/ids/0` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/spiffe/ids/1` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport0/tls/spiffe/trustDomain` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/dialKeepAlive` | `42s` |
| `traefik/tcp/serversTransports/TCPServersTransport1/dialTimeout` | `42s` |
| `traefik/tcp/serversTransports/TCPServersTransport1/terminationDelay` | `42s` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/certificates/0/certFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/certificates/0/keyFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/certificates/1/certFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/certificates/1/keyFile` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/insecureSkipVerify` | `true` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/peerCertURI` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/rootCAs/0` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/rootCAs/1` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/serverName` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/spiffe/ids/0` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/spiffe/ids/1` | `foobar` |
| `traefik/tcp/serversTransports/TCPServersTransport1/tls/spiffe/trustDomain` | `foobar` |
| `traefik/tcp/services/TCPService01/loadBalancer/proxyProtocol/version` | `42` |
| `traefik/tcp/services/TCPService01/loadBalancer/servers/0/address` | `foobar` |
| `traefik/tcp/services/TCPService01/loadBalancer/servers/0/tls` | `true` |
| `traefik/tcp/services/TCPService01/loadBalancer/servers/1/address` | `foobar` |
| `traefik/tcp/services/TCPService01/loadBalancer/servers/1/tls` | `true` |
| `traefik/tcp/services/TCPService01/loadBalancer/serversTransport` | `foobar` |
| `traefik/tcp/services/TCPService01/loadBalancer/terminationDelay` | `42` |
| `traefik/tcp/services/TCPService02/weighted/services/0/name` | `foobar` |
| `traefik/tcp/services/TCPService02/weighted/services/0/weight` | `42` |
| `traefik/tcp/services/TCPService02/weighted/services/1/name` | `foobar` |
| `traefik/tcp/services/TCPService02/weighted/services/1/weight` | `42` |
| `traefik/tls/certificates/0/certFile` | `foobar` |
| `traefik/tls/certificates/0/keyFile` | `foobar` |
| `traefik/tls/certificates/0/stores/0` | `foobar` |
| `traefik/tls/certificates/0/stores/1` | `foobar` |
| `traefik/tls/certificates/1/certFile` | `foobar` |
| `traefik/tls/certificates/1/keyFile` | `foobar` |
| `traefik/tls/certificates/1/stores/0` | `foobar` |
| `traefik/tls/certificates/1/stores/1` | `foobar` |
| `traefik/tls/options/Options0/alpnProtocols/0` | `foobar` |
| `traefik/tls/options/Options0/alpnProtocols/1` | `foobar` |
| `traefik/tls/options/Options0/cipherSuites/0` | `foobar` |
| `traefik/tls/options/Options0/cipherSuites/1` | `foobar` |
| `traefik/tls/options/Options0/clientAuth/caFiles/0` | `foobar` |
| `traefik/tls/options/Options0/clientAuth/caFiles/1` | `foobar` |
| `traefik/tls/options/Options0/clientAuth/clientAuthType` | `foobar` |
| `traefik/tls/options/Options0/curvePreferences/0` | `foobar` |
| `traefik/tls/options/Options0/curvePreferences/1` | `foobar` |
| `traefik/tls/options/Options0/disableSessionTickets` | `true` |
| `traefik/tls/options/Options0/maxVersion` | `foobar` |
| `traefik/tls/options/Options0/minVersion` | `foobar` |
| `traefik/tls/options/Options0/preferServerCipherSuites` | `true` |
| `traefik/tls/options/Options0/sniStrict` | `true` |
| `traefik/tls/options/Options1/alpnProtocols/0` | `foobar` |
| `traefik/tls/options/Options1/alpnProtocols/1` | `foobar` |
| `traefik/tls/options/Options1/cipherSuites/0` | `foobar` |
| `traefik/tls/options/Options1/cipherSuites/1` | `foobar` |
| `traefik/tls/options/Options1/clientAuth/caFiles/0` | `foobar` |
| `traefik/tls/options/Options1/clientAuth/caFiles/1` | `foobar` |
| `traefik/tls/options/Options1/clientAuth/clientAuthType` | `foobar` |
| `traefik/tls/options/Options1/curvePreferences/0` | `foobar` |
| `traefik/tls/options/Options1/curvePreferences/1` | `foobar` |
| `traefik/tls/options/Options1/disableSessionTickets` | `true` |
| `traefik/tls/options/Options1/maxVersion` | `foobar` |
| `traefik/tls/options/Options1/minVersion` | `foobar` |
| `traefik/tls/options/Options1/preferServerCipherSuites` | `true` |
| `traefik/tls/options/Options1/sniStrict` | `true` |
| `traefik/tls/stores/Store0/defaultCertificate/certFile` | `foobar` |
| `traefik/tls/stores/Store0/defaultCertificate/keyFile` | `foobar` |
| `traefik/tls/stores/Store0/defaultGeneratedCert/domain/main` | `foobar` |
| `traefik/tls/stores/Store0/defaultGeneratedCert/domain/sans/0` | `foobar` |
| `traefik/tls/stores/Store0/defaultGeneratedCert/domain/sans/1` | `foobar` |
| `traefik/tls/stores/Store0/defaultGeneratedCert/resolver` | `foobar` |
| `traefik/tls/stores/Store1/defaultCertificate/certFile` | `foobar` |
| `traefik/tls/stores/Store1/defaultCertificate/keyFile` | `foobar` |
| `traefik/tls/stores/Store1/defaultGeneratedCert/domain/main` | `foobar` |
| `traefik/tls/stores/Store1/defaultGeneratedCert/domain/sans/0` | `foobar` |
| `traefik/tls/stores/Store1/defaultGeneratedCert/domain/sans/1` | `foobar` |
| `traefik/tls/stores/Store1/defaultGeneratedCert/resolver` | `foobar` |
| `traefik/udp/routers/UDPRouter0/entryPoints/0` | `foobar` |
| `traefik/udp/routers/UDPRouter0/entryPoints/1` | `foobar` |
| `traefik/udp/routers/UDPRouter0/service` | `foobar` |
| `traefik/udp/routers/UDPRouter1/entryPoints/0` | `foobar` |
| `traefik/udp/routers/UDPRouter1/entryPoints/1` | `foobar` |
| `traefik/udp/routers/UDPRouter1/service` | `foobar` |
| `traefik/udp/services/UDPService01/loadBalancer/servers/0/address` | `foobar` |
| `traefik/udp/services/UDPService01/loadBalancer/servers/1/address` | `foobar` |
| `traefik/udp/services/UDPService02/weighted/services/0/name` | `foobar` |
| `traefik/udp/services/UDPService02/weighted/services/0/weight` | `42` |
| `traefik/udp/services/UDPService02/weighted/services/1/name` | `foobar` |
| `traefik/udp/services/UDPService02/weighted/services/1/weight` | `42` |
