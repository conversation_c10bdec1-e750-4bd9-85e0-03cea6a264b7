## CODE GENERATED AUTOMATICALLY
## THIS FILE MUST NOT BE EDITED BY HAND
http:
  routers:
    Router0:
      entryPoints:
        - foobar
        - foobar
      middlewares:
        - foobar
        - foobar
      service: foobar
      rule: foobar
      ruleSyntax: foobar
      priority: 42
      tls:
        options: foobar
        certResolver: foobar
        domains:
          - main: foobar
            sans:
              - foobar
              - foobar
          - main: foobar
            sans:
              - foobar
              - foobar
      observability:
        accessLogs: true
        tracing: true
        metrics: true
    Router1:
      entryPoints:
        - foobar
        - foobar
      middlewares:
        - foobar
        - foobar
      service: foobar
      rule: foobar
      ruleSyntax: foobar
      priority: 42
      tls:
        options: foobar
        certResolver: foobar
        domains:
          - main: foobar
            sans:
              - foobar
              - foobar
          - main: foobar
            sans:
              - foobar
              - foobar
      observability:
        accessLogs: true
        tracing: true
        metrics: true
  services:
    Service01:
      failover:
        service: foobar
        fallback: foobar
        healthCheck: {}
    Service02:
      loadBalancer:
        sticky:
          cookie:
            name: foobar
            secure: true
            httpOnly: true
            sameSite: foobar
            maxAge: 42
            path: foobar
            domain: foobar
        servers:
          - url: foobar
            weight: 42
            preservePath: true
          - url: foobar
            weight: 42
            preservePath: true
        strategy: foobar
        healthCheck:
          scheme: foobar
          mode: foobar
          path: foobar
          method: foobar
          status: 42
          port: 42
          interval: 42s
          unhealthyInterval: 42s
          timeout: 42s
          hostname: foobar
          followRedirects: true
          headers:
            name0: foobar
            name1: foobar
        passHostHeader: true
        responseForwarding:
          flushInterval: 42s
        serversTransport: foobar
    Service03:
      mirroring:
        service: foobar
        mirrorBody: true
        maxBodySize: 42
        mirrors:
          - name: foobar
            percent: 42
          - name: foobar
            percent: 42
        healthCheck: {}
    Service04:
      weighted:
        services:
          - name: foobar
            weight: 42
          - name: foobar
            weight: 42
        sticky:
          cookie:
            name: foobar
            secure: true
            httpOnly: true
            sameSite: foobar
            maxAge: 42
            path: foobar
            domain: foobar
        healthCheck: {}
  middlewares:
    Middleware01:
      addPrefix:
        prefix: foobar
    Middleware02:
      basicAuth:
        users:
          - foobar
          - foobar
        usersFile: foobar
        realm: foobar
        removeHeader: true
        headerField: foobar
    Middleware03:
      buffering:
        maxRequestBodyBytes: 42
        memRequestBodyBytes: 42
        maxResponseBodyBytes: 42
        memResponseBodyBytes: 42
        retryExpression: foobar
    Middleware04:
      chain:
        middlewares:
          - foobar
          - foobar
    Middleware05:
      circuitBreaker:
        expression: foobar
        checkPeriod: 42s
        fallbackDuration: 42s
        recoveryDuration: 42s
        responseCode: 42
    Middleware06:
      compress:
        excludedContentTypes:
          - foobar
          - foobar
        includedContentTypes:
          - foobar
          - foobar
        minResponseBodyBytes: 42
        encodings:
          - foobar
          - foobar
        defaultEncoding: foobar
    Middleware07:
      contentType:
        autoDetect: true
    Middleware08:
      digestAuth:
        users:
          - foobar
          - foobar
        usersFile: foobar
        removeHeader: true
        realm: foobar
        headerField: foobar
    Middleware09:
      errors:
        status:
          - foobar
          - foobar
        statusRewrites:
          name0: 42
          name1: 42
        service: foobar
        query: foobar
    Middleware10:
      forwardAuth:
        address: foobar
        tls:
          ca: foobar
          cert: foobar
          key: foobar
          insecureSkipVerify: true
          caOptional: true
        trustForwardHeader: true
        authResponseHeaders:
          - foobar
          - foobar
        authResponseHeadersRegex: foobar
        authRequestHeaders:
          - foobar
          - foobar
        addAuthCookiesToResponse:
          - foobar
          - foobar
        headerField: foobar
        forwardBody: true
        maxBodySize: 42
        preserveLocationHeader: true
        preserveRequestMethod: true
    Middleware11:
      grpcWeb:
        allowOrigins:
          - foobar
          - foobar
    Middleware12:
      headers:
        customRequestHeaders:
          name0: foobar
          name1: foobar
        customResponseHeaders:
          name0: foobar
          name1: foobar
        accessControlAllowCredentials: true
        accessControlAllowHeaders:
          - foobar
          - foobar
        accessControlAllowMethods:
          - foobar
          - foobar
        accessControlAllowOriginList:
          - foobar
          - foobar
        accessControlAllowOriginListRegex:
          - foobar
          - foobar
        accessControlExposeHeaders:
          - foobar
          - foobar
        accessControlMaxAge: 42
        addVaryHeader: true
        allowedHosts:
          - foobar
          - foobar
        hostsProxyHeaders:
          - foobar
          - foobar
        sslProxyHeaders:
          name0: foobar
          name1: foobar
        stsSeconds: 42
        stsIncludeSubdomains: true
        stsPreload: true
        forceSTSHeader: true
        frameDeny: true
        customFrameOptionsValue: foobar
        contentTypeNosniff: true
        browserXssFilter: true
        customBrowserXSSValue: foobar
        contentSecurityPolicy: foobar
        contentSecurityPolicyReportOnly: foobar
        publicKey: foobar
        referrerPolicy: foobar
        permissionsPolicy: foobar
        isDevelopment: true
        featurePolicy: foobar
        sslRedirect: true
        sslTemporaryRedirect: true
        sslHost: foobar
        sslForceHost: true
    Middleware13:
      ipAllowList:
        sourceRange:
          - foobar
          - foobar
        ipStrategy:
          depth: 42
          excludedIPs:
            - foobar
            - foobar
          ipv6Subnet: 42
        rejectStatusCode: 42
    Middleware14:
      ipWhiteList:
        sourceRange:
          - foobar
          - foobar
        ipStrategy:
          depth: 42
          excludedIPs:
            - foobar
            - foobar
          ipv6Subnet: 42
    Middleware15:
      inFlightReq:
        amount: 42
        sourceCriterion:
          ipStrategy:
            depth: 42
            excludedIPs:
              - foobar
              - foobar
            ipv6Subnet: 42
          requestHeaderName: foobar
          requestHost: true
    Middleware16:
      passTLSClientCert:
        pem: true
        info:
          notAfter: true
          notBefore: true
          sans: true
          serialNumber: true
          subject:
            country: true
            province: true
            locality: true
            organization: true
            organizationalUnit: true
            commonName: true
            serialNumber: true
            domainComponent: true
          issuer:
            country: true
            province: true
            locality: true
            organization: true
            commonName: true
            serialNumber: true
            domainComponent: true
    Middleware17:
      plugin:
        PluginConf0:
          name0: foobar
          name1: foobar
        PluginConf1:
          name0: foobar
          name1: foobar
    Middleware18:
      rateLimit:
        average: 42
        period: 42s
        burst: 42
        sourceCriterion:
          ipStrategy:
            depth: 42
            excludedIPs:
              - foobar
              - foobar
            ipv6Subnet: 42
          requestHeaderName: foobar
          requestHost: true
        redis:
          endpoints:
            - foobar
            - foobar
          tls:
            ca: foobar
            cert: foobar
            key: foobar
            insecureSkipVerify: true
          username: foobar
          password: foobar
          db: 42
          poolSize: 42
          minIdleConns: 42
          maxActiveConns: 42
          readTimeout: 42s
          writeTimeout: 42s
          dialTimeout: 42s
    Middleware19:
      redirectRegex:
        regex: foobar
        replacement: foobar
        permanent: true
    Middleware20:
      redirectScheme:
        scheme: foobar
        port: foobar
        permanent: true
    Middleware21:
      replacePath:
        path: foobar
    Middleware22:
      replacePathRegex:
        regex: foobar
        replacement: foobar
    Middleware23:
      retry:
        attempts: 42
        initialInterval: 42s
    Middleware24:
      stripPrefix:
        prefixes:
          - foobar
          - foobar
        forceSlash: true
    Middleware25:
      stripPrefixRegex:
        regex:
          - foobar
          - foobar
  serversTransports:
    ServersTransport0:
      serverName: foobar
      insecureSkipVerify: true
      rootCAs:
        - foobar
        - foobar
      certificates:
        - certFile: foobar
          keyFile: foobar
        - certFile: foobar
          keyFile: foobar
      maxIdleConnsPerHost: 42
      forwardingTimeouts:
        dialTimeout: 42s
        responseHeaderTimeout: 42s
        idleConnTimeout: 42s
        readIdleTimeout: 42s
        pingTimeout: 42s
      disableHTTP2: true
      peerCertURI: foobar
      spiffe:
        ids:
          - foobar
          - foobar
        trustDomain: foobar
    ServersTransport1:
      serverName: foobar
      insecureSkipVerify: true
      rootCAs:
        - foobar
        - foobar
      certificates:
        - certFile: foobar
          keyFile: foobar
        - certFile: foobar
          keyFile: foobar
      maxIdleConnsPerHost: 42
      forwardingTimeouts:
        dialTimeout: 42s
        responseHeaderTimeout: 42s
        idleConnTimeout: 42s
        readIdleTimeout: 42s
        pingTimeout: 42s
      disableHTTP2: true
      peerCertURI: foobar
      spiffe:
        ids:
          - foobar
          - foobar
        trustDomain: foobar
tcp:
  routers:
    TCPRouter0:
      entryPoints:
        - foobar
        - foobar
      middlewares:
        - foobar
        - foobar
      service: foobar
      rule: foobar
      ruleSyntax: foobar
      priority: 42
      tls:
        passthrough: true
        options: foobar
        certResolver: foobar
        domains:
          - main: foobar
            sans:
              - foobar
              - foobar
          - main: foobar
            sans:
              - foobar
              - foobar
    TCPRouter1:
      entryPoints:
        - foobar
        - foobar
      middlewares:
        - foobar
        - foobar
      service: foobar
      rule: foobar
      ruleSyntax: foobar
      priority: 42
      tls:
        passthrough: true
        options: foobar
        certResolver: foobar
        domains:
          - main: foobar
            sans:
              - foobar
              - foobar
          - main: foobar
            sans:
              - foobar
              - foobar
  services:
    TCPService01:
      loadBalancer:
        proxyProtocol:
          version: 42
        servers:
          - address: foobar
            tls: true
          - address: foobar
            tls: true
        serversTransport: foobar
        terminationDelay: 42
    TCPService02:
      weighted:
        services:
          - name: foobar
            weight: 42
          - name: foobar
            weight: 42
  middlewares:
    TCPMiddleware01:
      ipAllowList:
        sourceRange:
          - foobar
          - foobar
    TCPMiddleware02:
      ipWhiteList:
        sourceRange:
          - foobar
          - foobar
    TCPMiddleware03:
      inFlightConn:
        amount: 42
  serversTransports:
    TCPServersTransport0:
      dialKeepAlive: 42s
      dialTimeout: 42s
      terminationDelay: 42s
      tls:
        serverName: foobar
        insecureSkipVerify: true
        rootCAs:
          - foobar
          - foobar
        certificates:
          - certFile: foobar
            keyFile: foobar
          - certFile: foobar
            keyFile: foobar
        peerCertURI: foobar
        spiffe:
          ids:
            - foobar
            - foobar
          trustDomain: foobar
    TCPServersTransport1:
      dialKeepAlive: 42s
      dialTimeout: 42s
      terminationDelay: 42s
      tls:
        serverName: foobar
        insecureSkipVerify: true
        rootCAs:
          - foobar
          - foobar
        certificates:
          - certFile: foobar
            keyFile: foobar
          - certFile: foobar
            keyFile: foobar
        peerCertURI: foobar
        spiffe:
          ids:
            - foobar
            - foobar
          trustDomain: foobar
udp:
  routers:
    UDPRouter0:
      entryPoints:
        - foobar
        - foobar
      service: foobar
    UDPRouter1:
      entryPoints:
        - foobar
        - foobar
      service: foobar
  services:
    UDPService01:
      loadBalancer:
        servers:
          - address: foobar
          - address: foobar
    UDPService02:
      weighted:
        services:
          - name: foobar
            weight: 42
          - name: foobar
            weight: 42
tls:
  certificates:
    - certFile: foobar
      keyFile: foobar
      stores:
        - foobar
        - foobar
    - certFile: foobar
      keyFile: foobar
      stores:
        - foobar
        - foobar
  options:
    Options0:
      minVersion: foobar
      maxVersion: foobar
      cipherSuites:
        - foobar
        - foobar
      curvePreferences:
        - foobar
        - foobar
      clientAuth:
        caFiles:
          - foobar
          - foobar
        clientAuthType: foobar
      sniStrict: true
      alpnProtocols:
        - foobar
        - foobar
      disableSessionTickets: true
      preferServerCipherSuites: true
    Options1:
      minVersion: foobar
      maxVersion: foobar
      cipherSuites:
        - foobar
        - foobar
      curvePreferences:
        - foobar
        - foobar
      clientAuth:
        caFiles:
          - foobar
          - foobar
        clientAuthType: foobar
      sniStrict: true
      alpnProtocols:
        - foobar
        - foobar
      disableSessionTickets: true
      preferServerCipherSuites: true
  stores:
    Store0:
      defaultCertificate:
        certFile: foobar
        keyFile: foobar
      defaultGeneratedCert:
        resolver: foobar
        domain:
          main: foobar
          sans:
            - foobar
            - foobar
    Store1:
      defaultCertificate:
        certFile: foobar
        keyFile: foobar
      defaultGeneratedCert:
        resolver: foobar
        domain:
          main: foobar
          sans:
            - foobar
            - foobar
