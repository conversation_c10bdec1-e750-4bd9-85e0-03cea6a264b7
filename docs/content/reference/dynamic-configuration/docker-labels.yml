## CODE GENERATED AUTOMATICALLY
## THIS FILE MUST NOT BE EDITED BY HAND
- "traefik.http.middlewares.middleware01.addprefix.prefix=foobar"
- "traefik.http.middlewares.middleware02.basicauth.headerfield=foobar"
- "traefik.http.middlewares.middleware02.basicauth.realm=foobar"
- "traefik.http.middlewares.middleware02.basicauth.removeheader=true"
- "traefik.http.middlewares.middleware02.basicauth.users=foobar, foobar"
- "traefik.http.middlewares.middleware02.basicauth.usersfile=foobar"
- "traefik.http.middlewares.middleware03.buffering.maxrequestbodybytes=42"
- "traefik.http.middlewares.middleware03.buffering.maxresponsebodybytes=42"
- "traefik.http.middlewares.middleware03.buffering.memrequestbodybytes=42"
- "traefik.http.middlewares.middleware03.buffering.memresponsebodybytes=42"
- "traefik.http.middlewares.middleware03.buffering.retryexpression=foobar"
- "traefik.http.middlewares.middleware04.chain.middlewares=foobar, foobar"
- "traefik.http.middlewares.middleware05.circuitbreaker.checkperiod=42s"
- "traefik.http.middlewares.middleware05.circuitbreaker.expression=foobar"
- "traefik.http.middlewares.middleware05.circuitbreaker.fallbackduration=42s"
- "traefik.http.middlewares.middleware05.circuitbreaker.recoveryduration=42s"
- "traefik.http.middlewares.middleware05.circuitbreaker.responsecode=42"
- "traefik.http.middlewares.middleware06.compress=true"
- "traefik.http.middlewares.middleware06.compress.defaultencoding=foobar"
- "traefik.http.middlewares.middleware06.compress.encodings=foobar, foobar"
- "traefik.http.middlewares.middleware06.compress.excludedcontenttypes=foobar, foobar"
- "traefik.http.middlewares.middleware06.compress.includedcontenttypes=foobar, foobar"
- "traefik.http.middlewares.middleware06.compress.minresponsebodybytes=42"
- "traefik.http.middlewares.middleware07.contenttype=true"
- "traefik.http.middlewares.middleware07.contenttype.autodetect=true"
- "traefik.http.middlewares.middleware08.digestauth.headerfield=foobar"
- "traefik.http.middlewares.middleware08.digestauth.realm=foobar"
- "traefik.http.middlewares.middleware08.digestauth.removeheader=true"
- "traefik.http.middlewares.middleware08.digestauth.users=foobar, foobar"
- "traefik.http.middlewares.middleware08.digestauth.usersfile=foobar"
- "traefik.http.middlewares.middleware09.errors.query=foobar"
- "traefik.http.middlewares.middleware09.errors.service=foobar"
- "traefik.http.middlewares.middleware09.errors.status=foobar, foobar"
- "traefik.http.middlewares.middleware09.errors.statusrewrites.name0=42"
- "traefik.http.middlewares.middleware09.errors.statusrewrites.name1=42"
- "traefik.http.middlewares.middleware10.forwardauth.addauthcookiestoresponse=foobar, foobar"
- "traefik.http.middlewares.middleware10.forwardauth.address=foobar"
- "traefik.http.middlewares.middleware10.forwardauth.authrequestheaders=foobar, foobar"
- "traefik.http.middlewares.middleware10.forwardauth.authresponseheaders=foobar, foobar"
- "traefik.http.middlewares.middleware10.forwardauth.authresponseheadersregex=foobar"
- "traefik.http.middlewares.middleware10.forwardauth.forwardbody=true"
- "traefik.http.middlewares.middleware10.forwardauth.headerfield=foobar"
- "traefik.http.middlewares.middleware10.forwardauth.maxbodysize=42"
- "traefik.http.middlewares.middleware10.forwardauth.preservelocationheader=true"
- "traefik.http.middlewares.middleware10.forwardauth.preserverequestmethod=true"
- "traefik.http.middlewares.middleware10.forwardauth.tls.ca=foobar"
- "traefik.http.middlewares.middleware10.forwardauth.tls.caoptional=true"
- "traefik.http.middlewares.middleware10.forwardauth.tls.cert=foobar"
- "traefik.http.middlewares.middleware10.forwardauth.tls.insecureskipverify=true"
- "traefik.http.middlewares.middleware10.forwardauth.tls.key=foobar"
- "traefik.http.middlewares.middleware10.forwardauth.trustforwardheader=true"
- "traefik.http.middlewares.middleware11.grpcweb.alloworigins=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.accesscontrolallowcredentials=true"
- "traefik.http.middlewares.middleware12.headers.accesscontrolallowheaders=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.accesscontrolallowmethods=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.accesscontrolalloworiginlist=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.accesscontrolalloworiginlistregex=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.accesscontrolexposeheaders=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.accesscontrolmaxage=42"
- "traefik.http.middlewares.middleware12.headers.addvaryheader=true"
- "traefik.http.middlewares.middleware12.headers.allowedhosts=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.browserxssfilter=true"
- "traefik.http.middlewares.middleware12.headers.contentsecuritypolicy=foobar"
- "traefik.http.middlewares.middleware12.headers.contentsecuritypolicyreportonly=foobar"
- "traefik.http.middlewares.middleware12.headers.contenttypenosniff=true"
- "traefik.http.middlewares.middleware12.headers.custombrowserxssvalue=foobar"
- "traefik.http.middlewares.middleware12.headers.customframeoptionsvalue=foobar"
- "traefik.http.middlewares.middleware12.headers.customrequestheaders.name0=foobar"
- "traefik.http.middlewares.middleware12.headers.customrequestheaders.name1=foobar"
- "traefik.http.middlewares.middleware12.headers.customresponseheaders.name0=foobar"
- "traefik.http.middlewares.middleware12.headers.customresponseheaders.name1=foobar"
- "traefik.http.middlewares.middleware12.headers.featurepolicy=foobar"
- "traefik.http.middlewares.middleware12.headers.forcestsheader=true"
- "traefik.http.middlewares.middleware12.headers.framedeny=true"
- "traefik.http.middlewares.middleware12.headers.hostsproxyheaders=foobar, foobar"
- "traefik.http.middlewares.middleware12.headers.isdevelopment=true"
- "traefik.http.middlewares.middleware12.headers.permissionspolicy=foobar"
- "traefik.http.middlewares.middleware12.headers.publickey=foobar"
- "traefik.http.middlewares.middleware12.headers.referrerpolicy=foobar"
- "traefik.http.middlewares.middleware12.headers.sslforcehost=true"
- "traefik.http.middlewares.middleware12.headers.sslhost=foobar"
- "traefik.http.middlewares.middleware12.headers.sslproxyheaders.name0=foobar"
- "traefik.http.middlewares.middleware12.headers.sslproxyheaders.name1=foobar"
- "traefik.http.middlewares.middleware12.headers.sslredirect=true"
- "traefik.http.middlewares.middleware12.headers.ssltemporaryredirect=true"
- "traefik.http.middlewares.middleware12.headers.stsincludesubdomains=true"
- "traefik.http.middlewares.middleware12.headers.stspreload=true"
- "traefik.http.middlewares.middleware12.headers.stsseconds=42"
- "traefik.http.middlewares.middleware13.ipallowlist.ipstrategy=true"
- "traefik.http.middlewares.middleware13.ipallowlist.ipstrategy.depth=42"
- "traefik.http.middlewares.middleware13.ipallowlist.ipstrategy.excludedips=foobar, foobar"
- "traefik.http.middlewares.middleware13.ipallowlist.ipstrategy.ipv6subnet=42"
- "traefik.http.middlewares.middleware13.ipallowlist.rejectstatuscode=42"
- "traefik.http.middlewares.middleware13.ipallowlist.sourcerange=foobar, foobar"
- "traefik.http.middlewares.middleware14.ipwhitelist.ipstrategy=true"
- "traefik.http.middlewares.middleware14.ipwhitelist.ipstrategy.depth=42"
- "traefik.http.middlewares.middleware14.ipwhitelist.ipstrategy.excludedips=foobar, foobar"
- "traefik.http.middlewares.middleware14.ipwhitelist.ipstrategy.ipv6subnet=42"
- "traefik.http.middlewares.middleware14.ipwhitelist.sourcerange=foobar, foobar"
- "traefik.http.middlewares.middleware15.inflightreq.amount=42"
- "traefik.http.middlewares.middleware15.inflightreq.sourcecriterion.ipstrategy.depth=42"
- "traefik.http.middlewares.middleware15.inflightreq.sourcecriterion.ipstrategy.excludedips=foobar, foobar"
- "traefik.http.middlewares.middleware15.inflightreq.sourcecriterion.ipstrategy.ipv6subnet=42"
- "traefik.http.middlewares.middleware15.inflightreq.sourcecriterion.requestheadername=foobar"
- "traefik.http.middlewares.middleware15.inflightreq.sourcecriterion.requesthost=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.issuer.commonname=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.issuer.country=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.issuer.domaincomponent=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.issuer.locality=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.issuer.organization=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.issuer.province=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.issuer.serialnumber=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.notafter=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.notbefore=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.sans=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.serialnumber=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.commonname=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.country=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.domaincomponent=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.locality=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.organization=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.organizationalunit=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.province=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.info.subject.serialnumber=true"
- "traefik.http.middlewares.middleware16.passtlsclientcert.pem=true"
- "traefik.http.middlewares.middleware17.plugin.pluginconf0.name0=foobar"
- "traefik.http.middlewares.middleware17.plugin.pluginconf0.name1=foobar"
- "traefik.http.middlewares.middleware17.plugin.pluginconf1.name0=foobar"
- "traefik.http.middlewares.middleware17.plugin.pluginconf1.name1=foobar"
- "traefik.http.middlewares.middleware18.ratelimit.average=42"
- "traefik.http.middlewares.middleware18.ratelimit.burst=42"
- "traefik.http.middlewares.middleware18.ratelimit.period=42s"
- "traefik.http.middlewares.middleware18.ratelimit.redis.db=42"
- "traefik.http.middlewares.middleware18.ratelimit.redis.dialtimeout=42s"
- "traefik.http.middlewares.middleware18.ratelimit.redis.endpoints=foobar, foobar"
- "traefik.http.middlewares.middleware18.ratelimit.redis.maxactiveconns=42"
- "traefik.http.middlewares.middleware18.ratelimit.redis.minidleconns=42"
- "traefik.http.middlewares.middleware18.ratelimit.redis.password=foobar"
- "traefik.http.middlewares.middleware18.ratelimit.redis.poolsize=42"
- "traefik.http.middlewares.middleware18.ratelimit.redis.readtimeout=42s"
- "traefik.http.middlewares.middleware18.ratelimit.redis.tls.ca=foobar"
- "traefik.http.middlewares.middleware18.ratelimit.redis.tls.cert=foobar"
- "traefik.http.middlewares.middleware18.ratelimit.redis.tls.insecureskipverify=true"
- "traefik.http.middlewares.middleware18.ratelimit.redis.tls.key=foobar"
- "traefik.http.middlewares.middleware18.ratelimit.redis.username=foobar"
- "traefik.http.middlewares.middleware18.ratelimit.redis.writetimeout=42s"
- "traefik.http.middlewares.middleware18.ratelimit.sourcecriterion.ipstrategy.depth=42"
- "traefik.http.middlewares.middleware18.ratelimit.sourcecriterion.ipstrategy.excludedips=foobar, foobar"
- "traefik.http.middlewares.middleware18.ratelimit.sourcecriterion.ipstrategy.ipv6subnet=42"
- "traefik.http.middlewares.middleware18.ratelimit.sourcecriterion.requestheadername=foobar"
- "traefik.http.middlewares.middleware18.ratelimit.sourcecriterion.requesthost=true"
- "traefik.http.middlewares.middleware19.redirectregex.permanent=true"
- "traefik.http.middlewares.middleware19.redirectregex.regex=foobar"
- "traefik.http.middlewares.middleware19.redirectregex.replacement=foobar"
- "traefik.http.middlewares.middleware20.redirectscheme.permanent=true"
- "traefik.http.middlewares.middleware20.redirectscheme.port=foobar"
- "traefik.http.middlewares.middleware20.redirectscheme.scheme=foobar"
- "traefik.http.middlewares.middleware21.replacepath.path=foobar"
- "traefik.http.middlewares.middleware22.replacepathregex.regex=foobar"
- "traefik.http.middlewares.middleware22.replacepathregex.replacement=foobar"
- "traefik.http.middlewares.middleware23.retry.attempts=42"
- "traefik.http.middlewares.middleware23.retry.initialinterval=42s"
- "traefik.http.middlewares.middleware24.stripprefix.forceslash=true"
- "traefik.http.middlewares.middleware24.stripprefix.prefixes=foobar, foobar"
- "traefik.http.middlewares.middleware25.stripprefixregex.regex=foobar, foobar"
- "traefik.http.routers.router0.entrypoints=foobar, foobar"
- "traefik.http.routers.router0.middlewares=foobar, foobar"
- "traefik.http.routers.router0.observability.accesslogs=true"
- "traefik.http.routers.router0.observability.metrics=true"
- "traefik.http.routers.router0.observability.tracing=true"
- "traefik.http.routers.router0.priority=42"
- "traefik.http.routers.router0.rule=foobar"
- "traefik.http.routers.router0.rulesyntax=foobar"
- "traefik.http.routers.router0.service=foobar"
- "traefik.http.routers.router0.tls=true"
- "traefik.http.routers.router0.tls.certresolver=foobar"
- "traefik.http.routers.router0.tls.domains[0].main=foobar"
- "traefik.http.routers.router0.tls.domains[0].sans=foobar, foobar"
- "traefik.http.routers.router0.tls.domains[1].main=foobar"
- "traefik.http.routers.router0.tls.domains[1].sans=foobar, foobar"
- "traefik.http.routers.router0.tls.options=foobar"
- "traefik.http.routers.router1.entrypoints=foobar, foobar"
- "traefik.http.routers.router1.middlewares=foobar, foobar"
- "traefik.http.routers.router1.observability.accesslogs=true"
- "traefik.http.routers.router1.observability.metrics=true"
- "traefik.http.routers.router1.observability.tracing=true"
- "traefik.http.routers.router1.priority=42"
- "traefik.http.routers.router1.rule=foobar"
- "traefik.http.routers.router1.rulesyntax=foobar"
- "traefik.http.routers.router1.service=foobar"
- "traefik.http.routers.router1.tls=true"
- "traefik.http.routers.router1.tls.certresolver=foobar"
- "traefik.http.routers.router1.tls.domains[0].main=foobar"
- "traefik.http.routers.router1.tls.domains[0].sans=foobar, foobar"
- "traefik.http.routers.router1.tls.domains[1].main=foobar"
- "traefik.http.routers.router1.tls.domains[1].sans=foobar, foobar"
- "traefik.http.routers.router1.tls.options=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.followredirects=true"
- "traefik.http.services.service02.loadbalancer.healthcheck.headers.name0=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.headers.name1=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.hostname=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.interval=42s"
- "traefik.http.services.service02.loadbalancer.healthcheck.method=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.mode=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.path=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.port=42"
- "traefik.http.services.service02.loadbalancer.healthcheck.scheme=foobar"
- "traefik.http.services.service02.loadbalancer.healthcheck.status=42"
- "traefik.http.services.service02.loadbalancer.healthcheck.timeout=42s"
- "traefik.http.services.service02.loadbalancer.healthcheck.unhealthyinterval=42s"
- "traefik.http.services.service02.loadbalancer.passhostheader=true"
- "traefik.http.services.service02.loadbalancer.responseforwarding.flushinterval=42s"
- "traefik.http.services.service02.loadbalancer.serverstransport=foobar"
- "traefik.http.services.service02.loadbalancer.sticky=true"
- "traefik.http.services.service02.loadbalancer.sticky.cookie=true"
- "traefik.http.services.service02.loadbalancer.sticky.cookie.domain=foobar"
- "traefik.http.services.service02.loadbalancer.sticky.cookie.httponly=true"
- "traefik.http.services.service02.loadbalancer.sticky.cookie.maxage=42"
- "traefik.http.services.service02.loadbalancer.sticky.cookie.name=foobar"
- "traefik.http.services.service02.loadbalancer.sticky.cookie.path=foobar"
- "traefik.http.services.service02.loadbalancer.sticky.cookie.samesite=foobar"
- "traefik.http.services.service02.loadbalancer.sticky.cookie.secure=true"
- "traefik.http.services.service02.loadbalancer.strategy=foobar"
- "traefik.http.services.service02.loadbalancer.server.port=foobar"
- "traefik.http.services.service02.loadbalancer.server.preservepath=true"
- "traefik.http.services.service02.loadbalancer.server.scheme=foobar"
- "traefik.http.services.service02.loadbalancer.server.url=foobar"
- "traefik.http.services.service02.loadbalancer.server.weight=42"
- "traefik.tcp.middlewares.tcpmiddleware01.ipallowlist.sourcerange=foobar, foobar"
- "traefik.tcp.middlewares.tcpmiddleware02.ipwhitelist.sourcerange=foobar, foobar"
- "traefik.tcp.middlewares.tcpmiddleware03.inflightconn.amount=42"
- "traefik.tcp.routers.tcprouter0.entrypoints=foobar, foobar"
- "traefik.tcp.routers.tcprouter0.middlewares=foobar, foobar"
- "traefik.tcp.routers.tcprouter0.priority=42"
- "traefik.tcp.routers.tcprouter0.rule=foobar"
- "traefik.tcp.routers.tcprouter0.rulesyntax=foobar"
- "traefik.tcp.routers.tcprouter0.service=foobar"
- "traefik.tcp.routers.tcprouter0.tls=true"
- "traefik.tcp.routers.tcprouter0.tls.certresolver=foobar"
- "traefik.tcp.routers.tcprouter0.tls.domains[0].main=foobar"
- "traefik.tcp.routers.tcprouter0.tls.domains[0].sans=foobar, foobar"
- "traefik.tcp.routers.tcprouter0.tls.domains[1].main=foobar"
- "traefik.tcp.routers.tcprouter0.tls.domains[1].sans=foobar, foobar"
- "traefik.tcp.routers.tcprouter0.tls.options=foobar"
- "traefik.tcp.routers.tcprouter0.tls.passthrough=true"
- "traefik.tcp.routers.tcprouter1.entrypoints=foobar, foobar"
- "traefik.tcp.routers.tcprouter1.middlewares=foobar, foobar"
- "traefik.tcp.routers.tcprouter1.priority=42"
- "traefik.tcp.routers.tcprouter1.rule=foobar"
- "traefik.tcp.routers.tcprouter1.rulesyntax=foobar"
- "traefik.tcp.routers.tcprouter1.service=foobar"
- "traefik.tcp.routers.tcprouter1.tls=true"
- "traefik.tcp.routers.tcprouter1.tls.certresolver=foobar"
- "traefik.tcp.routers.tcprouter1.tls.domains[0].main=foobar"
- "traefik.tcp.routers.tcprouter1.tls.domains[0].sans=foobar, foobar"
- "traefik.tcp.routers.tcprouter1.tls.domains[1].main=foobar"
- "traefik.tcp.routers.tcprouter1.tls.domains[1].sans=foobar, foobar"
- "traefik.tcp.routers.tcprouter1.tls.options=foobar"
- "traefik.tcp.routers.tcprouter1.tls.passthrough=true"
- "traefik.tcp.services.tcpservice01.loadbalancer.proxyprotocol=true"
- "traefik.tcp.services.tcpservice01.loadbalancer.proxyprotocol.version=42"
- "traefik.tcp.services.tcpservice01.loadbalancer.serverstransport=foobar"
- "traefik.tcp.services.tcpservice01.loadbalancer.terminationdelay=42"
- "traefik.tcp.services.tcpservice01.loadbalancer.server.port=foobar"
- "traefik.tcp.services.tcpservice01.loadbalancer.server.tls=true"
- "traefik.tls.stores.store0.defaultgeneratedcert.domain.main=foobar"
- "traefik.tls.stores.store0.defaultgeneratedcert.domain.sans=foobar, foobar"
- "traefik.tls.stores.store0.defaultgeneratedcert.resolver=foobar"
- "traefik.tls.stores.store1.defaultgeneratedcert.domain.main=foobar"
- "traefik.tls.stores.store1.defaultgeneratedcert.domain.sans=foobar, foobar"
- "traefik.tls.stores.store1.defaultgeneratedcert.resolver=foobar"
- "traefik.udp.routers.udprouter0.entrypoints=foobar, foobar"
- "traefik.udp.routers.udprouter0.service=foobar"
- "traefik.udp.routers.udprouter1.entrypoints=foobar, foobar"
- "traefik.udp.routers.udprouter1.service=foobar"
- "traefik.udp.services.udpservice01.loadbalancer.server.port=foobar"
