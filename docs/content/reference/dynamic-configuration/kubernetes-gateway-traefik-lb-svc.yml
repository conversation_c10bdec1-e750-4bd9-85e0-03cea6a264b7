---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: traefik-controller

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: traefik

spec:
  replicas: 1
  selector:
    matchLabels:
      app: traefik-lb

  template:
    metadata:
      labels:
        app: traefik-lb

    spec:
      serviceAccountName: traefik-controller
      containers:
        - name: traefik
          image: traefik:v3.5
          args:
            - --entryPoints.web.address=:80
            - --entryPoints.websecure.address=:443
            - --experimental.kubernetesgateway
            - --providers.kubernetesgateway

          ports:
            - name: web
              containerPort: 80

            - name: websecure
              containerPort: 443

---
apiVersion: v1
kind: Service
metadata:
  name: traefik

spec:
  type: LoadBalancer
  selector:
    app: traefik-lb

  ports:
    - protocol: TCP
      port: 80
      targetPort: web
      name: web

    - protocol: TCP
      port: 443
      targetPort: websecure
      name: websecure
