---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: my-gateway-class
spec:
  controllerName: traefik.io/gateway-controller

---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: my-gateway
  namespace: default
spec:
  gatewayClassName: my-gateway-class
  listeners: # Use GatewayClass defaults for listener definition.
    - name: http
      protocol: HTTP
      port: 80

    - name: https
      protocol: HTTPS
      port: 443
      tls:
        certificateRefs:
          - kind: Secret
            name: mysecret

    - name: tcp
      protocol: TCP
      port: 9000
      allowedRoutes:
        kinds:
          - kind: TCPRoute

    - name: tls
      protocol: TLS
      port: 9443
      hostname: example.com
      tls:
        certificateRefs:
          - kind: Secret
            name: mysecret

---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: http-app
  namespace: default

spec:
  parentRefs:
    - name: my-gateway

  hostnames:
    - foo.com

  rules:
    - matches:
        - path:
            type: Exact
            value: /bar

      backendRefs:
        - name: whoami
          port: 80
          weight: 1

    - matches:
        - path:
            type: PathPrefix
            value: /foo

      backendRefs:
        - group: traefik.io
          kind: TraefikService
          name: myservice@file
          weight: 1
          port: 80

---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TCPRoute
metadata:
  name: tcp-app
  namespace: default

spec:
  parentRefs:
    - name: my-gateway

  rules:
    - backendRefs:
        - name: whoamitcp
          port: 9000
          weight: 1

---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TLSRoute
metadata:
  name: tls-app
  namespace: default

spec:
  parentRefs:
    - name: my-gateway
      sectionName: tls

  rules:
    - backendRefs:
        - name: whoamitcp
          port: 9000
          weight: 1
