---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: whoami

spec:
  replicas: 2
  selector:
    matchLabels:
      app: whoami

  template:
    metadata:
      labels:
        app: whoami

    spec:
      containers:
        - name: whoami
          image: traefik/whoami

---
apiVersion: v1
kind: Service
metadata:
  name: whoami

spec:
  selector:
    app: whoami

  ports:
    - protocol: TCP
      port: 80
