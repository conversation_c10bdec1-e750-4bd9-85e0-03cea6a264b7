## CODE GENERATED AUTOMATICALLY
## THIS FILE MUST NOT BE EDITED BY HAND
global:
  checkNewVersion: true
  sendAnonymousUsage: true
serversTransport:
  insecureSkipVerify: true
  rootCAs:
    - foobar
    - foobar
  maxIdleConnsPerHost: 42
  forwardingTimeouts:
    dialTimeout: 42s
    responseHeaderTimeout: 42s
    idleConnTimeout: 42s
  spiffe:
    ids:
      - foobar
      - foobar
    trustDomain: foobar
tcpServersTransport:
  dialKeepAlive: 42s
  dialTimeout: 42s
  terminationDelay: 42s
  tls:
    insecureSkipVerify: true
    rootCAs:
      - foobar
      - foobar
    spiffe:
      ids:
        - foobar
        - foobar
      trustDomain: foobar
entryPoints:
  EntryPoint0:
    address: foobar
    allowACMEByPass: true
    reusePort: true
    asDefault: true
    transport:
      lifeCycle:
        requestAcceptGraceTimeout: 42s
        graceTimeOut: 42s
      respondingTimeouts:
        readTimeout: 42s
        writeTimeout: 42s
        idleTimeout: 42s
      keepAliveMaxTime: 42s
      keepAliveMaxRequests: 42
    proxyProtocol:
      insecure: true
      trustedIPs:
        - foobar
        - foobar
    forwardedHeaders:
      insecure: true
      trustedIPs:
        - foobar
        - foobar
      connection:
        - foobar
        - foobar
    http:
      redirections:
        entryPoint:
          to: foobar
          scheme: foobar
          permanent: true
          priority: 42
      middlewares:
        - foobar
        - foobar
      tls:
        options: foobar
        certResolver: foobar
        domains:
          - main: foobar
            sans:
              - foobar
              - foobar
          - main: foobar
            sans:
              - foobar
              - foobar
      encodeQuerySemicolons: true
      sanitizePath: true
      maxHeaderBytes: 42
    http2:
      maxConcurrentStreams: 42
    http3:
      advertisedPort: 42
    udp:
      timeout: 42s
    observability:
      accessLogs: true
      tracing: true
      metrics: true
providers:
  providersThrottleDuration: 42s
  docker:
    exposedByDefault: true
    constraints: foobar
    allowEmptyServices: true
    network: foobar
    useBindPortIP: true
    watch: true
    defaultRule: foobar
    username: foobar
    password: foobar
    endpoint: foobar
    tls:
      ca: foobar
      cert: foobar
      key: foobar
      insecureSkipVerify: true
    httpClientTimeout: 42s
  swarm:
    exposedByDefault: true
    constraints: foobar
    allowEmptyServices: true
    network: foobar
    useBindPortIP: true
    watch: true
    defaultRule: foobar
    username: foobar
    password: foobar
    endpoint: foobar
    tls:
      ca: foobar
      cert: foobar
      key: foobar
      insecureSkipVerify: true
    httpClientTimeout: 42s
    refreshSeconds: 42s
  file:
    directory: foobar
    watch: true
    filename: foobar
    debugLogGeneratedTemplate: true
  kubernetesIngress:
    endpoint: foobar
    token: foobar
    certAuthFilePath: foobar
    namespaces:
      - foobar
      - foobar
    labelSelector: foobar
    ingressClass: foobar
    ingressEndpoint:
      ip: foobar
      hostname: foobar
      publishedService: foobar
    throttleDuration: 42s
    allowEmptyServices: true
    allowExternalNameServices: true
    disableIngressClassLookup: true
    disableClusterScopeResources: true
    nativeLBByDefault: true
    strictPrefixMatching: true
  kubernetesIngressNGINX:
    endpoint: foobar
    token: foobar
    certAuthFilePath: foobar
    throttleDuration: 42s
    watchNamespace: foobar
    watchNamespaceSelector: foobar
    ingressClass: foobar
    controllerClass: foobar
    watchIngressWithoutClass: true
    ingressClassByName: true
    publishService: foobar
    publishStatusAddress:
      - foobar
      - foobar
    defaultBackendService: foobar
    disableSvcExternalName: true
  kubernetesCRD:
    endpoint: foobar
    token: foobar
    certAuthFilePath: foobar
    namespaces:
      - foobar
      - foobar
    allowCrossNamespace: true
    allowExternalNameServices: true
    labelSelector: foobar
    ingressClass: foobar
    throttleDuration: 42s
    allowEmptyServices: true
    nativeLBByDefault: true
    disableClusterScopeResources: true
  kubernetesGateway:
    endpoint: foobar
    token: foobar
    certAuthFilePath: foobar
    namespaces:
      - foobar
      - foobar
    labelSelector: foobar
    throttleDuration: 42s
    experimentalChannel: true
    statusAddress:
      ip: foobar
      hostname: foobar
      service:
        name: foobar
        namespace: foobar
    nativeLBByDefault: true
  rest:
    insecure: true
  consulCatalog:
    constraints: foobar
    endpoint:
      address: foobar
      scheme: foobar
      datacenter: foobar
      token: foobar
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      httpAuth:
        username: foobar
        password: foobar
      endpointWaitTime: 42s
    prefix: foobar
    refreshInterval: 42s
    requireConsistent: true
    stale: true
    cache: true
    exposedByDefault: true
    defaultRule: foobar
    connectAware: true
    connectByDefault: true
    serviceName: foobar
    watch: true
    strictChecks:
      - foobar
      - foobar
    namespaces:
      - foobar
      - foobar
  nomad:
    defaultRule: foobar
    constraints: foobar
    endpoint:
      address: foobar
      region: foobar
      token: foobar
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      endpointWaitTime: 42s
    prefix: foobar
    stale: true
    exposedByDefault: true
    refreshInterval: 42s
    allowEmptyServices: true
    watch: true
    throttleDuration: 42s
    namespaces:
      - foobar
      - foobar
  ecs:
    constraints: foobar
    exposedByDefault: true
    refreshSeconds: 42
    defaultRule: foobar
    clusters:
      - foobar
      - foobar
    autoDiscoverClusters: true
    healthyTasksOnly: true
    ecsAnywhere: true
    region: foobar
    accessKeyID: foobar
    secretAccessKey: foobar
  consul:
    rootKey: foobar
    endpoints:
      - foobar
      - foobar
    token: foobar
    tls:
      ca: foobar
      cert: foobar
      key: foobar
      insecureSkipVerify: true
    namespaces:
      - foobar
      - foobar
  etcd:
    rootKey: foobar
    endpoints:
      - foobar
      - foobar
    tls:
      ca: foobar
      cert: foobar
      key: foobar
      insecureSkipVerify: true
    username: foobar
    password: foobar
  zooKeeper:
    rootKey: foobar
    endpoints:
      - foobar
      - foobar
    username: foobar
    password: foobar
  redis:
    rootKey: foobar
    endpoints:
      - foobar
      - foobar
    tls:
      ca: foobar
      cert: foobar
      key: foobar
      insecureSkipVerify: true
    username: foobar
    password: foobar
    db: 42
    sentinel:
      masterName: foobar
      username: foobar
      password: foobar
      latencyStrategy: true
      randomStrategy: true
      replicaStrategy: true
      useDisconnectedReplicas: true
  http:
    endpoint: foobar
    pollInterval: 42s
    pollTimeout: 42s
    headers:
      name0: foobar
      name1: foobar
    tls:
      ca: foobar
      cert: foobar
      key: foobar
      insecureSkipVerify: true
  plugin:
    PluginConf0:
      name0: foobar
      name1: foobar
    PluginConf1:
      name0: foobar
      name1: foobar
api:
  basePath: foobar
  insecure: true
  dashboard: true
  debug: true
  disableDashboardAd: true
metrics:
  addInternals: true
  prometheus:
    buckets:
      - 42
      - 42
    addEntryPointsLabels: true
    addRoutersLabels: true
    addServicesLabels: true
    entryPoint: foobar
    manualRouting: true
    headerLabels:
      name0: foobar
      name1: foobar
  datadog:
    address: foobar
    pushInterval: 42s
    addEntryPointsLabels: true
    addRoutersLabels: true
    addServicesLabels: true
    prefix: foobar
  statsD:
    address: foobar
    pushInterval: 42s
    addEntryPointsLabels: true
    addRoutersLabels: true
    addServicesLabels: true
    prefix: foobar
  influxDB2:
    address: foobar
    token: foobar
    pushInterval: 42s
    org: foobar
    bucket: foobar
    addEntryPointsLabels: true
    addRoutersLabels: true
    addServicesLabels: true
    additionalLabels:
      name0: foobar
      name1: foobar
  otlp:
    grpc:
      endpoint: foobar
      insecure: true
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
    http:
      endpoint: foobar
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
    addEntryPointsLabels: true
    addRoutersLabels: true
    addServicesLabels: true
    explicitBoundaries:
      - 42
      - 42
    pushInterval: 42s
    serviceName: foobar
ping:
  entryPoint: foobar
  manualRouting: true
  terminatingStatusCode: 42
log:
  level: foobar
  format: foobar
  noColor: true
  filePath: foobar
  maxSize: 42
  maxAge: 42
  maxBackups: 42
  compress: true
  otlp:
    serviceName: foobar
    resourceAttributes:
      name0: foobar
      name1: foobar
    grpc:
      endpoint: foobar
      insecure: true
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
    http:
      endpoint: foobar
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
accessLog:
  filePath: foobar
  format: foobar
  filters:
    statusCodes:
      - foobar
      - foobar
    retryAttempts: true
    minDuration: 42s
  fields:
    defaultMode: foobar
    names:
      name0: foobar
      name1: foobar
    headers:
      defaultMode: foobar
      names:
        name0: foobar
        name1: foobar
  bufferingSize: 42
  addInternals: true
  otlp:
    serviceName: foobar
    resourceAttributes:
      name0: foobar
      name1: foobar
    grpc:
      endpoint: foobar
      insecure: true
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
    http:
      endpoint: foobar
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
tracing:
  serviceName: foobar
  resourceAttributes:
    name0: foobar
    name1: foobar
  capturedRequestHeaders:
    - foobar
    - foobar
  capturedResponseHeaders:
    - foobar
    - foobar
  safeQueryParams:
    - foobar
    - foobar
  sampleRate: 42
  addInternals: true
  otlp:
    grpc:
      endpoint: foobar
      insecure: true
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
    http:
      endpoint: foobar
      tls:
        ca: foobar
        cert: foobar
        key: foobar
        insecureSkipVerify: true
      headers:
        name0: foobar
        name1: foobar
  globalAttributes:
    name0: foobar
    name1: foobar
hostResolver:
  cnameFlattening: true
  resolvConfig: foobar
  resolvDepth: 42
certificatesResolvers:
  CertificateResolver0:
    acme:
      email: foobar
      caServer: foobar
      preferredChain: foobar
      profile: foobar
      emailAddresses:
        - foobar
        - foobar
      storage: foobar
      keyType: foobar
      eab:
        kid: foobar
        hmacEncoded: foobar
      certificatesDuration: 42
      clientTimeout: 42s
      clientResponseHeaderTimeout: 42s
      caCertificates:
        - foobar
        - foobar
      caSystemCertPool: true
      caServerName: foobar
      dnsChallenge:
        provider: foobar
        resolvers:
          - foobar
          - foobar
        propagation:
          disableChecks: true
          disableANSChecks: true
          requireAllRNS: true
          delayBeforeChecks: 42s
        delayBeforeCheck: 42s
        disablePropagationCheck: true
      httpChallenge:
        entryPoint: foobar
        delay: 42s
      tlsChallenge: {}
    tailscale: {}
  CertificateResolver1:
    acme:
      email: foobar
      caServer: foobar
      preferredChain: foobar
      profile: foobar
      emailAddresses:
        - foobar
        - foobar
      storage: foobar
      keyType: foobar
      eab:
        kid: foobar
        hmacEncoded: foobar
      certificatesDuration: 42
      clientTimeout: 42s
      clientResponseHeaderTimeout: 42s
      caCertificates:
        - foobar
        - foobar
      caSystemCertPool: true
      caServerName: foobar
      dnsChallenge:
        provider: foobar
        resolvers:
          - foobar
          - foobar
        propagation:
          disableChecks: true
          disableANSChecks: true
          requireAllRNS: true
          delayBeforeChecks: 42s
        delayBeforeCheck: 42s
        disablePropagationCheck: true
      httpChallenge:
        entryPoint: foobar
        delay: 42s
      tlsChallenge: {}
    tailscale: {}
experimental:
  plugins:
    Descriptor0:
      moduleName: foobar
      version: foobar
      settings:
        envs:
          - foobar
          - foobar
        mounts:
          - foobar
          - foobar
        useUnsafe: true
    Descriptor1:
      moduleName: foobar
      version: foobar
      settings:
        envs:
          - foobar
          - foobar
        mounts:
          - foobar
          - foobar
        useUnsafe: true
  localPlugins:
    LocalDescriptor0:
      moduleName: foobar
      settings:
        envs:
          - foobar
          - foobar
        mounts:
          - foobar
          - foobar
        useUnsafe: true
    LocalDescriptor1:
      moduleName: foobar
      settings:
        envs:
          - foobar
          - foobar
        mounts:
          - foobar
          - foobar
        useUnsafe: true
  abortOnPluginFailure: true
  fastProxy:
    debug: true
  otlplogs: true
  kubernetesIngressNGINX: true
  kubernetesGateway: true
core:
  defaultRuleSyntax: foobar
spiffe:
  workloadAPIAddr: foobar
ocsp:
  responderOverrides:
    name0: foobar
    name1: foobar
