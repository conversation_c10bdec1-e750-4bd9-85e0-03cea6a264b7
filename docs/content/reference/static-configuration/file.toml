## CODE GENERATED AUTOMATICALLY
## THIS FILE MUST NOT BE EDITED BY HAND
[global]
  checkNewVersion = true
  sendAnonymousUsage = true

[serversTransport]
  insecureSkipVerify = true
  rootCAs = ["foobar", "foobar"]
  maxIdleConnsPerHost = 42
  [serversTransport.forwardingTimeouts]
    dialTimeout = "42s"
    responseHeaderTimeout = "42s"
    idleConnTimeout = "42s"
  [serversTransport.spiffe]
    ids = ["foobar", "foobar"]
    trustDomain = "foobar"

[tcpServersTransport]
  dialKeepAlive = "42s"
  dialTimeout = "42s"
  terminationDelay = "42s"
  [tcpServersTransport.tls]
    insecureSkipVerify = true
    rootCAs = ["foobar", "foobar"]
    [tcpServersTransport.tls.spiffe]
      ids = ["foobar", "foobar"]
      trustDomain = "foobar"

[entryPoints]
  [entryPoints.EntryPoint0]
    address = "foobar"
    allowACMEByPass = true
    reusePort = true
    asDefault = true
    [entryPoints.EntryPoint0.transport]
      keepAliveMaxTime = "42s"
      keepAliveMaxRequests = 42
      [entryPoints.EntryPoint0.transport.lifeCycle]
        requestAcceptGraceTimeout = "42s"
        graceTimeOut = "42s"
      [entryPoints.EntryPoint0.transport.respondingTimeouts]
        readTimeout = "42s"
        writeTimeout = "42s"
        idleTimeout = "42s"
    [entryPoints.EntryPoint0.proxyProtocol]
      insecure = true
      trustedIPs = ["foobar", "foobar"]
    [entryPoints.EntryPoint0.forwardedHeaders]
      insecure = true
      trustedIPs = ["foobar", "foobar"]
      connection = ["foobar", "foobar"]
    [entryPoints.EntryPoint0.http]
      middlewares = ["foobar", "foobar"]
      encodeQuerySemicolons = true
      sanitizePath = true
      maxHeaderBytes = 42
      [entryPoints.EntryPoint0.http.redirections]
        [entryPoints.EntryPoint0.http.redirections.entryPoint]
          to = "foobar"
          scheme = "foobar"
          permanent = true
          priority = 42
      [entryPoints.EntryPoint0.http.tls]
        options = "foobar"
        certResolver = "foobar"

        [[entryPoints.EntryPoint0.http.tls.domains]]
          main = "foobar"
          sans = ["foobar", "foobar"]

        [[entryPoints.EntryPoint0.http.tls.domains]]
          main = "foobar"
          sans = ["foobar", "foobar"]
    [entryPoints.EntryPoint0.http2]
      maxConcurrentStreams = 42
    [entryPoints.EntryPoint0.http3]
      advertisedPort = 42
    [entryPoints.EntryPoint0.udp]
      timeout = "42s"
    [entryPoints.EntryPoint0.observability]
      accessLogs = true
      tracing = true
      metrics = true

[providers]
  providersThrottleDuration = "42s"
  [providers.docker]
    exposedByDefault = true
    constraints = "foobar"
    allowEmptyServices = true
    network = "foobar"
    useBindPortIP = true
    watch = true
    defaultRule = "foobar"
    username = "foobar"
    password = "foobar"
    endpoint = "foobar"
    httpClientTimeout = "42s"
    [providers.docker.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
  [providers.swarm]
    exposedByDefault = true
    constraints = "foobar"
    allowEmptyServices = true
    network = "foobar"
    useBindPortIP = true
    watch = true
    defaultRule = "foobar"
    username = "foobar"
    password = "foobar"
    endpoint = "foobar"
    httpClientTimeout = "42s"
    refreshSeconds = "42s"
    [providers.swarm.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
  [providers.file]
    directory = "foobar"
    watch = true
    filename = "foobar"
    debugLogGeneratedTemplate = true
  [providers.kubernetesIngress]
    endpoint = "foobar"
    token = "foobar"
    certAuthFilePath = "foobar"
    namespaces = ["foobar", "foobar"]
    labelSelector = "foobar"
    ingressClass = "foobar"
    throttleDuration = "42s"
    allowEmptyServices = true
    allowExternalNameServices = true
    disableIngressClassLookup = true
    disableClusterScopeResources = true
    nativeLBByDefault = true
    strictPrefixMatching = true
    [providers.kubernetesIngress.ingressEndpoint]
      ip = "foobar"
      hostname = "foobar"
      publishedService = "foobar"
  [providers.kubernetesIngressNGINX]
    endpoint = "foobar"
    token = "foobar"
    certAuthFilePath = "foobar"
    throttleDuration = "42s"
    watchNamespace = "foobar"
    watchNamespaceSelector = "foobar"
    ingressClass = "foobar"
    controllerClass = "foobar"
    watchIngressWithoutClass = true
    ingressClassByName = true
    publishService = "foobar"
    publishStatusAddress = ["foobar", "foobar"]
    defaultBackendService = "foobar"
    disableSvcExternalName = true
  [providers.kubernetesCRD]
    endpoint = "foobar"
    token = "foobar"
    certAuthFilePath = "foobar"
    namespaces = ["foobar", "foobar"]
    allowCrossNamespace = true
    allowExternalNameServices = true
    labelSelector = "foobar"
    ingressClass = "foobar"
    throttleDuration = "42s"
    allowEmptyServices = true
    nativeLBByDefault = true
    disableClusterScopeResources = true
  [providers.kubernetesGateway]
    endpoint = "foobar"
    token = "foobar"
    certAuthFilePath = "foobar"
    namespaces = ["foobar", "foobar"]
    labelSelector = "foobar"
    throttleDuration = "42s"
    experimentalChannel = true
    nativeLBByDefault = true
    [providers.kubernetesGateway.statusAddress]
      ip = "foobar"
      hostname = "foobar"
      [providers.kubernetesGateway.statusAddress.service]
        name = "foobar"
        namespace = "foobar"
  [providers.rest]
    insecure = true
  [providers.consulCatalog]
    constraints = "foobar"
    prefix = "foobar"
    refreshInterval = "42s"
    requireConsistent = true
    stale = true
    cache = true
    exposedByDefault = true
    defaultRule = "foobar"
    connectAware = true
    connectByDefault = true
    serviceName = "foobar"
    watch = true
    strictChecks = ["foobar", "foobar"]
    namespaces = ["foobar", "foobar"]
    [providers.consulCatalog.endpoint]
      address = "foobar"
      scheme = "foobar"
      datacenter = "foobar"
      token = "foobar"
      endpointWaitTime = "42s"
      [providers.consulCatalog.endpoint.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [providers.consulCatalog.endpoint.httpAuth]
        username = "foobar"
        password = "foobar"
  [providers.nomad]
    defaultRule = "foobar"
    constraints = "foobar"
    prefix = "foobar"
    stale = true
    exposedByDefault = true
    refreshInterval = "42s"
    allowEmptyServices = true
    watch = true
    throttleDuration = "42s"
    namespaces = ["foobar", "foobar"]
    [providers.nomad.endpoint]
      address = "foobar"
      region = "foobar"
      token = "foobar"
      endpointWaitTime = "42s"
      [providers.nomad.endpoint.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
  [providers.ecs]
    constraints = "foobar"
    exposedByDefault = true
    refreshSeconds = 42
    defaultRule = "foobar"
    clusters = ["foobar", "foobar"]
    autoDiscoverClusters = true
    healthyTasksOnly = true
    ecsAnywhere = true
    region = "foobar"
    accessKeyID = "foobar"
    secretAccessKey = "foobar"
  [providers.consul]
    rootKey = "foobar"
    endpoints = ["foobar", "foobar"]
    token = "foobar"
    namespaces = ["foobar", "foobar"]
    [providers.consul.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
  [providers.etcd]
    rootKey = "foobar"
    endpoints = ["foobar", "foobar"]
    username = "foobar"
    password = "foobar"
    [providers.etcd.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
  [providers.zooKeeper]
    rootKey = "foobar"
    endpoints = ["foobar", "foobar"]
    username = "foobar"
    password = "foobar"
  [providers.redis]
    rootKey = "foobar"
    endpoints = ["foobar", "foobar"]
    username = "foobar"
    password = "foobar"
    db = 42
    [providers.redis.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
    [providers.redis.sentinel]
      masterName = "foobar"
      username = "foobar"
      password = "foobar"
      latencyStrategy = true
      randomStrategy = true
      replicaStrategy = true
      useDisconnectedReplicas = true
  [providers.http]
    endpoint = "foobar"
    pollInterval = "42s"
    pollTimeout = "42s"
    [providers.http.headers]
      name0 = "foobar"
      name1 = "foobar"
    [providers.http.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
  [providers.plugin]
    [providers.plugin.PluginConf0]
      name0 = "foobar"
      name1 = "foobar"
    [providers.plugin.PluginConf1]
      name0 = "foobar"
      name1 = "foobar"

[api]
  basePath = "foobar"
  insecure = true
  dashboard = true
  debug = true
  disableDashboardAd = true

[metrics]
  addInternals = true
  [metrics.prometheus]
    buckets = [42.0, 42.0]
    addEntryPointsLabels = true
    addRoutersLabels = true
    addServicesLabels = true
    entryPoint = "foobar"
    manualRouting = true
    [metrics.prometheus.headerLabels]
      name0 = "foobar"
      name1 = "foobar"
  [metrics.datadog]
    address = "foobar"
    pushInterval = "42s"
    addEntryPointsLabels = true
    addRoutersLabels = true
    addServicesLabels = true
    prefix = "foobar"
  [metrics.statsD]
    address = "foobar"
    pushInterval = "42s"
    addEntryPointsLabels = true
    addRoutersLabels = true
    addServicesLabels = true
    prefix = "foobar"
  [metrics.influxDB2]
    address = "foobar"
    token = "foobar"
    pushInterval = "42s"
    org = "foobar"
    bucket = "foobar"
    addEntryPointsLabels = true
    addRoutersLabels = true
    addServicesLabels = true
    [metrics.influxDB2.additionalLabels]
      name0 = "foobar"
      name1 = "foobar"
  [metrics.otlp]
    addEntryPointsLabels = true
    addRoutersLabels = true
    addServicesLabels = true
    explicitBoundaries = [42.0, 42.0]
    pushInterval = "42s"
    serviceName = "foobar"
    [metrics.otlp.grpc]
      endpoint = "foobar"
      insecure = true
      [metrics.otlp.grpc.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [metrics.otlp.grpc.headers]
        name0 = "foobar"
        name1 = "foobar"
    [metrics.otlp.http]
      endpoint = "foobar"
      [metrics.otlp.http.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [metrics.otlp.http.headers]
        name0 = "foobar"
        name1 = "foobar"

[ping]
  entryPoint = "foobar"
  manualRouting = true
  terminatingStatusCode = 42

[log]
  level = "foobar"
  format = "foobar"
  noColor = true
  filePath = "foobar"
  maxSize = 42
  maxAge = 42
  maxBackups = 42
  compress = true
  [log.otlp]
    serviceName = "foobar"
    [log.otlp.resourceAttributes]
      name0 = "foobar"
      name1 = "foobar"
    [log.otlp.grpc]
      endpoint = "foobar"
      insecure = true
      [log.otlp.grpc.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [log.otlp.grpc.headers]
        name0 = "foobar"
        name1 = "foobar"
    [log.otlp.http]
      endpoint = "foobar"
      [log.otlp.http.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [log.otlp.http.headers]
        name0 = "foobar"
        name1 = "foobar"

[accessLog]
  filePath = "foobar"
  format = "foobar"
  bufferingSize = 42
  addInternals = true
  [accessLog.filters]
    statusCodes = ["foobar", "foobar"]
    retryAttempts = true
    minDuration = "42s"
  [accessLog.fields]
    defaultMode = "foobar"
    [accessLog.fields.names]
      name0 = "foobar"
      name1 = "foobar"
    [accessLog.fields.headers]
      defaultMode = "foobar"
      [accessLog.fields.headers.names]
        name0 = "foobar"
        name1 = "foobar"
  [accessLog.otlp]
    serviceName = "foobar"
    [accessLog.otlp.resourceAttributes]
      name0 = "foobar"
      name1 = "foobar"
    [accessLog.otlp.grpc]
      endpoint = "foobar"
      insecure = true
      [accessLog.otlp.grpc.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [accessLog.otlp.grpc.headers]
        name0 = "foobar"
        name1 = "foobar"
    [accessLog.otlp.http]
      endpoint = "foobar"
      [accessLog.otlp.http.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [accessLog.otlp.http.headers]
        name0 = "foobar"
        name1 = "foobar"

[tracing]
  serviceName = "foobar"
  capturedRequestHeaders = ["foobar", "foobar"]
  capturedResponseHeaders = ["foobar", "foobar"]
  safeQueryParams = ["foobar", "foobar"]
  sampleRate = 42.0
  addInternals = true
  [tracing.resourceAttributes]
    name0 = "foobar"
    name1 = "foobar"
  [tracing.otlp]
    [tracing.otlp.grpc]
      endpoint = "foobar"
      insecure = true
      [tracing.otlp.grpc.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [tracing.otlp.grpc.headers]
        name0 = "foobar"
        name1 = "foobar"
    [tracing.otlp.http]
      endpoint = "foobar"
      [tracing.otlp.http.tls]
        ca = "foobar"
        cert = "foobar"
        key = "foobar"
        insecureSkipVerify = true
      [tracing.otlp.http.headers]
        name0 = "foobar"
        name1 = "foobar"
  [tracing.globalAttributes]
    name0 = "foobar"
    name1 = "foobar"

[hostResolver]
  cnameFlattening = true
  resolvConfig = "foobar"
  resolvDepth = 42

[certificatesResolvers]
  [certificatesResolvers.CertificateResolver0]
    [certificatesResolvers.CertificateResolver0.acme]
      email = "foobar"
      caServer = "foobar"
      preferredChain = "foobar"
      profile = "foobar"
      emailAddresses = ["foobar", "foobar"]
      storage = "foobar"
      keyType = "foobar"
      certificatesDuration = 42
      clientTimeout = "42s"
      clientResponseHeaderTimeout = "42s"
      caCertificates = ["foobar", "foobar"]
      caSystemCertPool = true
      caServerName = "foobar"
      [certificatesResolvers.CertificateResolver0.acme.eab]
        kid = "foobar"
        hmacEncoded = "foobar"
      [certificatesResolvers.CertificateResolver0.acme.dnsChallenge]
        provider = "foobar"
        resolvers = ["foobar", "foobar"]
        delayBeforeCheck = "42s"
        disablePropagationCheck = true
        [certificatesResolvers.CertificateResolver0.acme.dnsChallenge.propagation]
          disableChecks = true
          disableANSChecks = true
          requireAllRNS = true
          delayBeforeChecks = "42s"
      [certificatesResolvers.CertificateResolver0.acme.httpChallenge]
        entryPoint = "foobar"
        delay = "42s"
      [certificatesResolvers.CertificateResolver0.acme.tlsChallenge]
    [certificatesResolvers.CertificateResolver0.tailscale]
  [certificatesResolvers.CertificateResolver1]
    [certificatesResolvers.CertificateResolver1.acme]
      email = "foobar"
      caServer = "foobar"
      preferredChain = "foobar"
      profile = "foobar"
      emailAddresses = ["foobar", "foobar"]
      storage = "foobar"
      keyType = "foobar"
      certificatesDuration = 42
      clientTimeout = "42s"
      clientResponseHeaderTimeout = "42s"
      caCertificates = ["foobar", "foobar"]
      caSystemCertPool = true
      caServerName = "foobar"
      [certificatesResolvers.CertificateResolver1.acme.eab]
        kid = "foobar"
        hmacEncoded = "foobar"
      [certificatesResolvers.CertificateResolver1.acme.dnsChallenge]
        provider = "foobar"
        resolvers = ["foobar", "foobar"]
        delayBeforeCheck = "42s"
        disablePropagationCheck = true
        [certificatesResolvers.CertificateResolver1.acme.dnsChallenge.propagation]
          disableChecks = true
          disableANSChecks = true
          requireAllRNS = true
          delayBeforeChecks = "42s"
      [certificatesResolvers.CertificateResolver1.acme.httpChallenge]
        entryPoint = "foobar"
        delay = "42s"
      [certificatesResolvers.CertificateResolver1.acme.tlsChallenge]
    [certificatesResolvers.CertificateResolver1.tailscale]

[experimental]
  abortOnPluginFailure = true
  otlplogs = true
  kubernetesIngressNGINX = true
  kubernetesGateway = true
  [experimental.plugins]
    [experimental.plugins.Descriptor0]
      moduleName = "foobar"
      version = "foobar"
      [experimental.plugins.Descriptor0.settings]
        envs = ["foobar", "foobar"]
        mounts = ["foobar", "foobar"]
        useUnsafe = true
    [experimental.plugins.Descriptor1]
      moduleName = "foobar"
      version = "foobar"
      [experimental.plugins.Descriptor1.settings]
        envs = ["foobar", "foobar"]
        mounts = ["foobar", "foobar"]
        useUnsafe = true
  [experimental.localPlugins]
    [experimental.localPlugins.LocalDescriptor0]
      moduleName = "foobar"
      [experimental.localPlugins.LocalDescriptor0.settings]
        envs = ["foobar", "foobar"]
        mounts = ["foobar", "foobar"]
        useUnsafe = true
    [experimental.localPlugins.LocalDescriptor1]
      moduleName = "foobar"
      [experimental.localPlugins.LocalDescriptor1.settings]
        envs = ["foobar", "foobar"]
        mounts = ["foobar", "foobar"]
        useUnsafe = true
  [experimental.fastProxy]
    debug = true

[core]
  defaultRuleSyntax = "foobar"

[spiffe]
  workloadAPIAddr = "foobar"

[ocsp]
  [ocsp.responderOverrides]
    name0 = "foobar"
    name1 = "foobar"
