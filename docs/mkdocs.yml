site_name: Traefik
site_description: Traefik Documentation
site_author: traefik.io
site_url: https://doc.traefik.io/traefik
dev_addr: 0.0.0.0:8000

repo_name: 'GitHub'
repo_url: 'https://github.com/traefik/traefik'

docs_dir: 'content'

product: proxy

# https://squidfunk.github.io/mkdocs-material/
theme:
  name: 'traefik-labs'
  language: en
  include_sidebar: true
  favicon: assets/img/traefikproxy-icon-color.png
  logo: assets/img/traefikproxy-vertical-logo-color.svg
  feature:
    tabs: false
  palette:
    primary: 'cyan'
    accent: 'cyan'
  i18n:
    prev: 'Previous'
    next: 'Next'

copyright: 'Traefik Labs • Copyright &copy; 2016-2025'

extra_javascript:
  - assets/js/hljs/highlight.pack.js # Download from https://highlightjs.org/download/ and enable YAML, TOML and Dockerfile
  - assets/js/extra.js

plugins:
  - search
  - exclude:
      glob:
        - "**/include-*.md"

# https://squidfunk.github.io/mkdocs-material/extensions/admonition/
# https://facelessuser.github.io/pymdown-extensions/
markdown_extensions:
  - meta
  - attr_list
  - admonition
  - footnotes
  - pymdownx.details
  - pymdownx.inlinehilite
  - pymdownx.highlight:
      use_pygments: false # hljs is used instead of pygment for TOML highlighting support
  - pymdownx.smartsymbols
  - pymdownx.superfences
  - pymdownx.tabbed
  - pymdownx.tasklist
  - pymdownx.snippets:
      check_paths: true
  - markdown_include.include:
      base_path: content/includes/
      encoding: utf-8
  - toc:
      permalink: true

# Page tree
nav:
  - 'What is Traefik': 'index.md'
  - 'Getting Started':
      - 'Overview': 'getting-started/index.md'
      - 'Quick Start':
        - 'Kubernetes': 'getting-started/kubernetes.md'
        - 'Docker': 'getting-started/docker.md'
      - 'Configuration Introduction': 'getting-started/configuration-overview.md'
      - 'Frequently Asked Questions': 'getting-started/faq.md'
  - 'Observe':
      - 'Overview': 'observe/overview.md'
      - 'Logs & Access Logs': 'observe/logs-and-access-logs.md'
      - 'Metrics': 'observe/metrics.md'
      - 'Tracing': 'observe/tracing.md'
  - 'Configuration Discovery':
      - 'Overview': 'providers/overview.md'
      - 'Docker': 'providers/docker.md'
      - 'Swarm': 'providers/swarm.md'
      - 'Kubernetes IngressRoute': 'providers/kubernetes-crd.md'
      - 'Kubernetes Ingress': 'providers/kubernetes-ingress.md'
      - 'Kubernetes Ingress NGINX': 'reference/install-configuration/providers/kubernetes/kubernetes-ingress-nginx.md'
      - 'Kubernetes Gateway API': 'providers/kubernetes-gateway.md'
      - 'Consul Catalog': 'providers/consul-catalog.md'
      - 'Nomad': 'providers/nomad.md'
      - 'ECS': 'providers/ecs.md'
      - 'File': 'providers/file.md'
      - 'Consul': 'providers/consul.md'
      - 'Etcd': 'providers/etcd.md'
      - 'ZooKeeper': 'providers/zookeeper.md'
      - 'Redis': 'providers/redis.md'
      - 'HTTP': 'providers/http.md'
  - 'Routing & Load Balancing':
      - 'Overview': 'routing/overview.md'
      - 'EntryPoints': 'routing/entrypoints.md'
      - 'Routers': 'routing/routers/index.md'
      - 'Services': 'routing/services/index.md'
      - 'Providers':
          - 'Docker': 'routing/providers/docker.md'
          - 'Swarm': 'routing/providers/swarm.md'
          - 'Kubernetes IngressRoute': 'routing/providers/kubernetes-crd.md'
          - 'Kubernetes Ingress': 'routing/providers/kubernetes-ingress.md'
          - 'Kubernetes Ingress NGINX': 'reference/routing-configuration/kubernetes/ingress-nginx.md'
          - 'Kubernetes Gateway API': 'routing/providers/kubernetes-gateway.md'
          - 'Consul Catalog': 'routing/providers/consul-catalog.md'
          - 'Nomad': 'routing/providers/nomad.md'
          - 'ECS': 'routing/providers/ecs.md'
          - 'KV': 'routing/providers/kv.md'
  - 'HTTPS & TLS':
      - 'Overview': 'https/overview.md'
      - 'TLS': 'https/tls.md'
      - 'Let''s Encrypt': 'https/acme.md'
      - 'Tailscale': 'https/tailscale.md'
      - 'SPIFFE': 'https/spiffe.md'
      - 'OCSP': 'https/ocsp.md'
  - 'Middlewares':
    - 'Overview': 'middlewares/overview.md'
    - 'HTTP':
        - 'Overview': 'middlewares/http/overview.md'
        - 'AddPrefix': 'middlewares/http/addprefix.md'
        - 'BasicAuth': 'middlewares/http/basicauth.md'
        - 'Buffering': 'middlewares/http/buffering.md'
        - 'Chain': 'middlewares/http/chain.md'
        - 'CircuitBreaker': 'middlewares/http/circuitbreaker.md'
        - 'Compress': 'middlewares/http/compress.md'
        - 'ContentType': 'middlewares/http/contenttype.md'
        - 'DigestAuth': 'middlewares/http/digestauth.md'
        - 'Errors': 'middlewares/http/errorpages.md'
        - 'ForwardAuth': 'middlewares/http/forwardauth.md'
        - 'GrpcWeb': 'middlewares/http/grpcweb.md'
        - 'Headers': 'middlewares/http/headers.md'
        - 'IPWhiteList': 'middlewares/http/ipwhitelist.md'
        - 'IPAllowList': 'middlewares/http/ipallowlist.md'
        - 'InFlightReq': 'middlewares/http/inflightreq.md'
        - 'PassTLSClientCert': 'middlewares/http/passtlsclientcert.md'
        - 'RateLimit': 'middlewares/http/ratelimit.md'
        - 'RedirectRegex': 'middlewares/http/redirectregex.md'
        - 'RedirectScheme': 'middlewares/http/redirectscheme.md'
        - 'ReplacePath': 'middlewares/http/replacepath.md'
        - 'ReplacePathRegex': 'middlewares/http/replacepathregex.md'
        - 'Retry': 'middlewares/http/retry.md'
        - 'StripPrefix': 'middlewares/http/stripprefix.md'
        - 'StripPrefixRegex': 'middlewares/http/stripprefixregex.md'
    - 'TCP':
        - 'Overview': 'middlewares/tcp/overview.md'
        - 'InFlightConn': 'middlewares/tcp/inflightconn.md'
        - 'IPWhiteList': 'middlewares/tcp/ipwhitelist.md'
        - 'IPAllowList': 'middlewares/tcp/ipallowlist.md'
  - 'Plugins & Plugin Catalog': 'plugins/index.md'
  - 'Operations':
      - 'CLI': 'operations/cli.md'
      - 'Dashboard' : 'operations/dashboard.md'
      - 'API': 'operations/api.md'
      - 'Ping': 'operations/ping.md'
  - 'Observability':
      - 'Overview': 'observability/overview.md'
      - 'Logs': 'observability/logs.md'
      - 'Access Logs': 'observability/access-logs.md'
      - 'Metrics':
          - 'Overview': 'observability/metrics/overview.md'
          - 'Datadog': 'observability/metrics/datadog.md'
          - 'InfluxDB2': 'observability/metrics/influxdb2.md'
          - 'OpenTelemetry': 'observability/metrics/opentelemetry.md'
          - 'Prometheus': 'observability/metrics/prometheus.md'
          - 'StatsD': 'observability/metrics/statsd.md'
      - 'Tracing':
          - 'Overview': 'observability/tracing/overview.md'
          - 'OpenTelemetry': 'observability/tracing/opentelemetry.md'
  - 'Security':
        - 'Content-Length': 'security/content-length.md'
        - 'TLS in Multi-Tenant Kubernetes': 'security/tls-certs-in-multi-tenant-kubernetes.md'
  - 'User Guides':
      - 'FastProxy': 'user-guides/fastproxy.md'
      - 'Kubernetes and Let''s Encrypt': 'user-guides/crd-acme/index.md'
      - 'Kubernetes and cert-manager': 'user-guides/cert-manager.md'
      - 'gRPC Examples': 'user-guides/grpc.md'
      - 'WebSocket Examples': 'user-guides/websocket.md'
      - 'Docker':
        - 'Basic Example': 'user-guides/docker-compose/basic-example/index.md'
        - 'HTTPS with Let''s Encrypt':
          - 'TLS Challenge': 'user-guides/docker-compose/acme-tls/index.md'
          - 'HTTP Challenge': 'user-guides/docker-compose/acme-http/index.md'
          - 'DNS Challenge': 'user-guides/docker-compose/acme-dns/index.md'
  - 'Migration':
      - 'Traefik v3 minor migrations': 'migration/v3.md'
      - 'Traefik v2 to v3':
        - 'Migration guide': 'migration/v2-to-v3.md'
        - 'Configuration changes for v3': 'migration/v2-to-v3-details.md'
      - 'Traefik v2 minor migrations': 'migration/v2.md'
      - 'Traefik v1 to v2': 'migration/v1-to-v2.md'
  - 'Contributing':
      - 'Thank You!': 'contributing/thank-you.md'
      - 'Submitting Issues': 'contributing/submitting-issues.md'
      - 'Submitting PRs': 'contributing/submitting-pull-requests.md'
      - 'Security': 'contributing/submitting-security-issues.md'
      - 'Building and Testing': 'contributing/building-testing.md'
      - 'Documentation': 'contributing/documentation.md'
      - 'Data Collection': 'contributing/data-collection.md'
      - 'Advocating': 'contributing/advocating.md'
      - 'Maintainers': 'contributing/maintainers.md'
  - 'Reference':
    - 'Install Configuration':
      - 'Boot Environment': 'reference/install-configuration/boot-environment.md'
      - 'Configuration Discovery':
        - 'Overview' : 'reference/install-configuration/providers/overview.md'
        - 'Kubernetes':
          - 'Kubernetes Gateway API' : 'reference/install-configuration/providers/kubernetes/kubernetes-gateway.md'
          - 'Kubernetes CRD' : 'reference/install-configuration/providers/kubernetes/kubernetes-crd.md'
          - 'Kubernetes Ingress' : 'reference/install-configuration/providers/kubernetes/kubernetes-ingress.md'
          - 'Kubernetes Ingress NGINX' : 'reference/install-configuration/providers/kubernetes/kubernetes-ingress-nginx.md'
        - 'Docker': 'reference/install-configuration/providers/docker.md'
        - 'Swarm': 'reference/install-configuration/providers/swarm.md'
        - 'Hashicorp':
          - 'Nomad': "reference/install-configuration/providers/hashicorp/nomad.md"
          - 'Consul': 'reference/install-configuration/providers/hashicorp/consul.md'
          - 'Consul Catalog': 'reference/install-configuration/providers/hashicorp/consul-catalog.md'
        - 'KV Stores':
          - 'Redis': 'reference/install-configuration/providers/kv/redis.md'
          - 'Consul': 'reference/install-configuration/providers/kv/consul.md'
          - 'etcd': 'reference/install-configuration/providers/kv/etcd.md'
          - 'ZooKeeper' : 'reference/install-configuration/providers/kv/zk.md'
        - 'Others':
          - 'File': 'reference/install-configuration/providers/others/file.md'
          - 'ECS': 'reference/install-configuration/providers/others/ecs.md'
          - 'HTTP': 'reference/install-configuration/providers/others/http.md'
      - 'EntryPoints': 'reference/install-configuration/entrypoints.md'
      - 'API & Dashboard': 'reference/install-configuration/api-dashboard.md'
      - 'TLS':
          - 'Certificate Resolvers':
            - "Overview" : 'reference/install-configuration/tls/certificate-resolvers/overview.md'
            - "ACME" : 'reference/install-configuration/tls/certificate-resolvers/acme.md'
            - "Tailscale" : 'reference/install-configuration/tls/certificate-resolvers/tailscale.md'
          - "SPIFFE" : 'reference/install-configuration/tls/spiffe.md'
          - "OCSP" : 'reference/install-configuration/tls/ocsp.md'
      - 'Observability':
          - 'Metrics' : 'reference/install-configuration/observability/metrics.md'
          - 'Tracing': 'reference/install-configuration/observability/tracing.md'
          - 'Logs & AccessLogs': 'reference/install-configuration/observability/logs-and-accesslogs.md'
          - 'Health Check (CLI & Ping)': 'reference/install-configuration/observability/healthcheck.md'
      # - 'Options List':  'reference/install-configuration/cli-options-list.md' -- Todo
    - 'Routing Configuration':
        - 'General' :
          - 'Configuration Methods' : 'reference/routing-configuration/dynamic-configuration-methods.md'
          - 'HTTP' :
            - 'Router' :
              - 'Rules & Priority' : 'reference/routing-configuration/http/router/rules-and-priority.md'
              - 'Observability': 'reference/routing-configuration/http/router/observability.md'
            - 'Load Balancing' :
              - 'Service' : 'reference/routing-configuration/http/load-balancing/service.md'
              - 'ServersTransport' : 'reference/routing-configuration/http/load-balancing/serverstransport.md'
            - 'TLS' :
              - 'Overview' : 'reference/routing-configuration/http/tls/overview.md'
              - 'TLS Certificates' : 'reference/routing-configuration/http/tls/tls-certificates.md'
              - 'TLS Options' : 'reference/routing-configuration/http/tls/tls-options.md'
            - 'Middlewares' :
              - 'Overview' : 'reference/routing-configuration/http/middlewares/overview.md'
              - 'AddPrefix' : 'reference/routing-configuration/http/middlewares/addprefix.md'
              - 'BasicAuth' : 'reference/routing-configuration/http/middlewares/basicauth.md'
              - 'Buffering': 'reference/routing-configuration/http/middlewares/buffering.md'
              - 'Chain': 'reference/routing-configuration/http/middlewares/chain.md'
              - 'Circuit Breaker' : 'reference/routing-configuration/http/middlewares/circuitbreaker.md'
              - 'Compress': 'reference/routing-configuration/http/middlewares/compress.md'
              - 'ContentType': 'reference/routing-configuration/http/middlewares/contenttype.md'
              - 'DigestAuth': 'reference/routing-configuration/http/middlewares/digestauth.md'
              - 'Errors': 'reference/routing-configuration/http/middlewares/errorpages.md'
              - 'ForwardAuth': 'reference/routing-configuration/http/middlewares/forwardauth.md'
              - 'GrpcWeb': 'reference/routing-configuration/http/middlewares/grpcweb.md'
              - 'Headers': 'reference/routing-configuration/http/middlewares/headers.md'
              - 'IPAllowList': 'reference/routing-configuration/http/middlewares/ipallowlist.md'
              - 'InFlightReq': 'reference/routing-configuration/http/middlewares/inflightreq.md'
              - 'PassTLSClientCert': 'reference/routing-configuration/http/middlewares/passtlsclientcert.md'
              - 'RateLimit': 'reference/routing-configuration/http/middlewares/ratelimit.md'
              - 'RedirectRegex': 'reference/routing-configuration/http/middlewares/redirectregex.md'
              - 'RedirectScheme': 'reference/routing-configuration/http/middlewares/redirectscheme.md'
              - 'ReplacePath': 'reference/routing-configuration/http/middlewares/replacepath.md'
              - 'ReplacePathRegex': 'reference/routing-configuration/http/middlewares/replacepathregex.md'
              - 'Retry': 'reference/routing-configuration/http/middlewares/retry.md'
              - 'StripPrefix': 'reference/routing-configuration/http/middlewares/stripprefix.md'
              - 'StripPrefixRegex': 'reference/routing-configuration/http/middlewares/stripprefixregex.md'
          - 'TCP' :
              - 'Router' :
                - 'Rules & Priority' : 'reference/routing-configuration/tcp/router/rules-and-priority.md'
              - 'Service' : 'reference/routing-configuration/tcp/service.md'
              - 'ServersTransport' : 'reference/routing-configuration/tcp/serverstransport.md'
              - 'TLS' : 'reference/routing-configuration/tcp/tls.md'
              - 'Middlewares' :
                - 'Overview' : 'reference/routing-configuration/tcp/middlewares/overview.md'
                - 'InFlightConn' : 'reference/routing-configuration/tcp/middlewares/inflightconn.md'
                - 'IPAllowList' : 'reference/routing-configuration/tcp/middlewares/ipallowlist.md'
          - 'UDP' :
            - 'Router' :
              - 'Rules & Priority' : 'reference/routing-configuration/udp/router/rules-priority.md'
            - 'Service' : 'reference/routing-configuration/udp/service.md'
        - 'Kubernetes':
          - 'Gateway API' : 'reference/routing-configuration/kubernetes/gateway-api.md'
          - 'Kubernetes CRD' :
            - 'HTTP' :
              - 'IngressRoute' : 'reference/routing-configuration/kubernetes/crd/http/ingressroute.md'
              - 'TraefikService' : 'reference/routing-configuration/kubernetes/crd/http/traefikservice.md'
              - 'ServersTransport' : 'reference/routing-configuration/kubernetes/crd/http/serverstransport.md'
              - 'Middleware' : 'reference/routing-configuration/kubernetes/crd/http/middleware.md'
              - 'TLSOption' : 'reference/routing-configuration/kubernetes/crd/http/tlsoption.md'
              - 'TLSStore' : 'reference/routing-configuration/kubernetes/crd/http/tlsstore.md'
            - 'TCP' :
                - 'IngressRouteTCP' : 'reference/routing-configuration/kubernetes/crd/tcp/ingressroutetcp.md'
                - 'ServersTransportTCP' : 'reference/routing-configuration/kubernetes/crd/tcp/serverstransporttcp.md'
                - 'MiddlewareTCP' : 'reference/routing-configuration/kubernetes/crd/tcp/middlewaretcp.md'
                - 'TLSOption' : 'reference/routing-configuration/kubernetes/crd/tcp/tlsoption.md'
                - 'TLSStore' : 'reference/routing-configuration/kubernetes/crd/tcp/tlsstore.md'
            - 'UDP' :
                - 'IngressRouteUDP' : 'reference/routing-configuration/kubernetes/crd/udp/ingressrouteudp.md'
          - 'Ingress' : 'reference/routing-configuration/kubernetes/ingress.md'
          - 'Ingress NGINX' : 'reference/routing-configuration/kubernetes/ingress-nginx.md'
        - 'Label & Tag Providers' :
          - 'Docker' : 'reference/routing-configuration/other-providers/docker.md'
          - 'Swarm' : 'reference/routing-configuration/other-providers/swarm.md'
          - 'Consul Catalog' : 'reference/routing-configuration/other-providers/consul-catalog.md'
          - 'Nomad' : 'reference/routing-configuration/other-providers/nomad.md'
          - 'ECS' : 'reference/routing-configuration/other-providers/ecs.md'
          - 'KV' : 'reference/routing-configuration/other-providers/kv.md'
  - 'Deprecation Notices':
      - 'Releases': 'deprecation/releases.md'
      - 'Features': 'deprecation/features.md'
