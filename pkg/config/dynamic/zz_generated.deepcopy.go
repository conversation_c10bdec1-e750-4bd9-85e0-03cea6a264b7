//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
The MIT License (MIT)

Copyright (c) 2016-2020 Containous SAS; 2020-2025 Traefik Labs

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

// Code generated by deepcopy-gen. DO NOT EDIT.

package dynamic

import (
	paersertypes "github.com/traefik/paerser/types"
	tls "github.com/traefik/traefik/v3/pkg/tls"
	types "github.com/traefik/traefik/v3/pkg/types"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AddPrefix) DeepCopyInto(out *AddPrefix) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AddPrefix.
func (in *AddPrefix) DeepCopy() *AddPrefix {
	if in == nil {
		return nil
	}
	out := new(AddPrefix)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BasicAuth) DeepCopyInto(out *BasicAuth) {
	*out = *in
	if in.Users != nil {
		in, out := &in.Users, &out.Users
		*out = make(Users, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BasicAuth.
func (in *BasicAuth) DeepCopy() *BasicAuth {
	if in == nil {
		return nil
	}
	out := new(BasicAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Buffering) DeepCopyInto(out *Buffering) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Buffering.
func (in *Buffering) DeepCopy() *Buffering {
	if in == nil {
		return nil
	}
	out := new(Buffering)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Chain) DeepCopyInto(out *Chain) {
	*out = *in
	if in.Middlewares != nil {
		in, out := &in.Middlewares, &out.Middlewares
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Chain.
func (in *Chain) DeepCopy() *Chain {
	if in == nil {
		return nil
	}
	out := new(Chain)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CircuitBreaker) DeepCopyInto(out *CircuitBreaker) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CircuitBreaker.
func (in *CircuitBreaker) DeepCopy() *CircuitBreaker {
	if in == nil {
		return nil
	}
	out := new(CircuitBreaker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientTLS) DeepCopyInto(out *ClientTLS) {
	*out = *in
	if in.CAOptional != nil {
		in, out := &in.CAOptional, &out.CAOptional
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientTLS.
func (in *ClientTLS) DeepCopy() *ClientTLS {
	if in == nil {
		return nil
	}
	out := new(ClientTLS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Compress) DeepCopyInto(out *Compress) {
	*out = *in
	if in.ExcludedContentTypes != nil {
		in, out := &in.ExcludedContentTypes, &out.ExcludedContentTypes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.IncludedContentTypes != nil {
		in, out := &in.IncludedContentTypes, &out.IncludedContentTypes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Encodings != nil {
		in, out := &in.Encodings, &out.Encodings
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Compress.
func (in *Compress) DeepCopy() *Compress {
	if in == nil {
		return nil
	}
	out := new(Compress)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Configuration) DeepCopyInto(out *Configuration) {
	*out = *in
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = new(TCPConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.UDP != nil {
		in, out := &in.UDP, &out.UDP
		*out = new(UDPConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(TLSConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Configuration.
func (in *Configuration) DeepCopy() *Configuration {
	if in == nil {
		return nil
	}
	out := new(Configuration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in Configurations) DeepCopyInto(out *Configurations) {
	{
		in := &in
		*out = make(Configurations, len(*in))
		for key, val := range *in {
			var outVal *Configuration
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(Configuration)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
		return
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Configurations.
func (in Configurations) DeepCopy() Configurations {
	if in == nil {
		return nil
	}
	out := new(Configurations)
	in.DeepCopyInto(out)
	return *out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ContentType) DeepCopyInto(out *ContentType) {
	*out = *in
	if in.AutoDetect != nil {
		in, out := &in.AutoDetect, &out.AutoDetect
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ContentType.
func (in *ContentType) DeepCopy() *ContentType {
	if in == nil {
		return nil
	}
	out := new(ContentType)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Cookie) DeepCopyInto(out *Cookie) {
	*out = *in
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Cookie.
func (in *Cookie) DeepCopy() *Cookie {
	if in == nil {
		return nil
	}
	out := new(Cookie)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DigestAuth) DeepCopyInto(out *DigestAuth) {
	*out = *in
	if in.Users != nil {
		in, out := &in.Users, &out.Users
		*out = make(Users, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DigestAuth.
func (in *DigestAuth) DeepCopy() *DigestAuth {
	if in == nil {
		return nil
	}
	out := new(DigestAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ErrorPage) DeepCopyInto(out *ErrorPage) {
	*out = *in
	if in.Status != nil {
		in, out := &in.Status, &out.Status
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.StatusRewrites != nil {
		in, out := &in.StatusRewrites, &out.StatusRewrites
		*out = make(map[string]int, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ErrorPage.
func (in *ErrorPage) DeepCopy() *ErrorPage {
	if in == nil {
		return nil
	}
	out := new(ErrorPage)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Failover) DeepCopyInto(out *Failover) {
	*out = *in
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheck)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Failover.
func (in *Failover) DeepCopy() *Failover {
	if in == nil {
		return nil
	}
	out := new(Failover)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ForwardAuth) DeepCopyInto(out *ForwardAuth) {
	*out = *in
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(ClientTLS)
		(*in).DeepCopyInto(*out)
	}
	if in.AuthResponseHeaders != nil {
		in, out := &in.AuthResponseHeaders, &out.AuthResponseHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AuthRequestHeaders != nil {
		in, out := &in.AuthRequestHeaders, &out.AuthRequestHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AddAuthCookiesToResponse != nil {
		in, out := &in.AddAuthCookiesToResponse, &out.AddAuthCookiesToResponse
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MaxBodySize != nil {
		in, out := &in.MaxBodySize, &out.MaxBodySize
		*out = new(int64)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ForwardAuth.
func (in *ForwardAuth) DeepCopy() *ForwardAuth {
	if in == nil {
		return nil
	}
	out := new(ForwardAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ForwardingTimeouts) DeepCopyInto(out *ForwardingTimeouts) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ForwardingTimeouts.
func (in *ForwardingTimeouts) DeepCopy() *ForwardingTimeouts {
	if in == nil {
		return nil
	}
	out := new(ForwardingTimeouts)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GRPCStatus) DeepCopyInto(out *GRPCStatus) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GRPCStatus.
func (in *GRPCStatus) DeepCopy() *GRPCStatus {
	if in == nil {
		return nil
	}
	out := new(GRPCStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GrpcWeb) DeepCopyInto(out *GrpcWeb) {
	*out = *in
	if in.AllowOrigins != nil {
		in, out := &in.AllowOrigins, &out.AllowOrigins
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GrpcWeb.
func (in *GrpcWeb) DeepCopy() *GrpcWeb {
	if in == nil {
		return nil
	}
	out := new(GrpcWeb)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPConfiguration) DeepCopyInto(out *HTTPConfiguration) {
	*out = *in
	if in.Routers != nil {
		in, out := &in.Routers, &out.Routers
		*out = make(map[string]*Router, len(*in))
		for key, val := range *in {
			var outVal *Router
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(Router)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make(map[string]*Service, len(*in))
		for key, val := range *in {
			var outVal *Service
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(Service)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.Middlewares != nil {
		in, out := &in.Middlewares, &out.Middlewares
		*out = make(map[string]*Middleware, len(*in))
		for key, val := range *in {
			var outVal *Middleware
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(Middleware)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.Models != nil {
		in, out := &in.Models, &out.Models
		*out = make(map[string]*Model, len(*in))
		for key, val := range *in {
			var outVal *Model
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(Model)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.ServersTransports != nil {
		in, out := &in.ServersTransports, &out.ServersTransports
		*out = make(map[string]*ServersTransport, len(*in))
		for key, val := range *in {
			var outVal *ServersTransport
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(ServersTransport)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPConfiguration.
func (in *HTTPConfiguration) DeepCopy() *HTTPConfiguration {
	if in == nil {
		return nil
	}
	out := new(HTTPConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HeaderModifier) DeepCopyInto(out *HeaderModifier) {
	*out = *in
	if in.Set != nil {
		in, out := &in.Set, &out.Set
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Add != nil {
		in, out := &in.Add, &out.Add
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Remove != nil {
		in, out := &in.Remove, &out.Remove
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HeaderModifier.
func (in *HeaderModifier) DeepCopy() *HeaderModifier {
	if in == nil {
		return nil
	}
	out := new(HeaderModifier)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Headers) DeepCopyInto(out *Headers) {
	*out = *in
	if in.CustomRequestHeaders != nil {
		in, out := &in.CustomRequestHeaders, &out.CustomRequestHeaders
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.CustomResponseHeaders != nil {
		in, out := &in.CustomResponseHeaders, &out.CustomResponseHeaders
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.AccessControlAllowHeaders != nil {
		in, out := &in.AccessControlAllowHeaders, &out.AccessControlAllowHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AccessControlAllowMethods != nil {
		in, out := &in.AccessControlAllowMethods, &out.AccessControlAllowMethods
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AccessControlAllowOriginList != nil {
		in, out := &in.AccessControlAllowOriginList, &out.AccessControlAllowOriginList
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AccessControlAllowOriginListRegex != nil {
		in, out := &in.AccessControlAllowOriginListRegex, &out.AccessControlAllowOriginListRegex
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AccessControlExposeHeaders != nil {
		in, out := &in.AccessControlExposeHeaders, &out.AccessControlExposeHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowedHosts != nil {
		in, out := &in.AllowedHosts, &out.AllowedHosts
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.HostsProxyHeaders != nil {
		in, out := &in.HostsProxyHeaders, &out.HostsProxyHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.SSLProxyHeaders != nil {
		in, out := &in.SSLProxyHeaders, &out.SSLProxyHeaders
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.FeaturePolicy != nil {
		in, out := &in.FeaturePolicy, &out.FeaturePolicy
		*out = new(string)
		**out = **in
	}
	if in.SSLRedirect != nil {
		in, out := &in.SSLRedirect, &out.SSLRedirect
		*out = new(bool)
		**out = **in
	}
	if in.SSLTemporaryRedirect != nil {
		in, out := &in.SSLTemporaryRedirect, &out.SSLTemporaryRedirect
		*out = new(bool)
		**out = **in
	}
	if in.SSLHost != nil {
		in, out := &in.SSLHost, &out.SSLHost
		*out = new(string)
		**out = **in
	}
	if in.SSLForceHost != nil {
		in, out := &in.SSLForceHost, &out.SSLForceHost
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Headers.
func (in *Headers) DeepCopy() *Headers {
	if in == nil {
		return nil
	}
	out := new(Headers)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HealthCheck) DeepCopyInto(out *HealthCheck) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HealthCheck.
func (in *HealthCheck) DeepCopy() *HealthCheck {
	if in == nil {
		return nil
	}
	out := new(HealthCheck)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IPAllowList) DeepCopyInto(out *IPAllowList) {
	*out = *in
	if in.SourceRange != nil {
		in, out := &in.SourceRange, &out.SourceRange
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.IPStrategy != nil {
		in, out := &in.IPStrategy, &out.IPStrategy
		*out = new(IPStrategy)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IPAllowList.
func (in *IPAllowList) DeepCopy() *IPAllowList {
	if in == nil {
		return nil
	}
	out := new(IPAllowList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IPStrategy) DeepCopyInto(out *IPStrategy) {
	*out = *in
	if in.ExcludedIPs != nil {
		in, out := &in.ExcludedIPs, &out.ExcludedIPs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.IPv6Subnet != nil {
		in, out := &in.IPv6Subnet, &out.IPv6Subnet
		*out = new(int)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IPStrategy.
func (in *IPStrategy) DeepCopy() *IPStrategy {
	if in == nil {
		return nil
	}
	out := new(IPStrategy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IPWhiteList) DeepCopyInto(out *IPWhiteList) {
	*out = *in
	if in.SourceRange != nil {
		in, out := &in.SourceRange, &out.SourceRange
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.IPStrategy != nil {
		in, out := &in.IPStrategy, &out.IPStrategy
		*out = new(IPStrategy)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IPWhiteList.
func (in *IPWhiteList) DeepCopy() *IPWhiteList {
	if in == nil {
		return nil
	}
	out := new(IPWhiteList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *InFlightReq) DeepCopyInto(out *InFlightReq) {
	*out = *in
	if in.SourceCriterion != nil {
		in, out := &in.SourceCriterion, &out.SourceCriterion
		*out = new(SourceCriterion)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new InFlightReq.
func (in *InFlightReq) DeepCopy() *InFlightReq {
	if in == nil {
		return nil
	}
	out := new(InFlightReq)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Message) DeepCopyInto(out *Message) {
	*out = *in
	if in.Configuration != nil {
		in, out := &in.Configuration, &out.Configuration
		*out = new(Configuration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Message.
func (in *Message) DeepCopy() *Message {
	if in == nil {
		return nil
	}
	out := new(Message)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Middleware) DeepCopyInto(out *Middleware) {
	*out = *in
	if in.AddPrefix != nil {
		in, out := &in.AddPrefix, &out.AddPrefix
		*out = new(AddPrefix)
		**out = **in
	}
	if in.StripPrefix != nil {
		in, out := &in.StripPrefix, &out.StripPrefix
		*out = new(StripPrefix)
		(*in).DeepCopyInto(*out)
	}
	if in.StripPrefixRegex != nil {
		in, out := &in.StripPrefixRegex, &out.StripPrefixRegex
		*out = new(StripPrefixRegex)
		(*in).DeepCopyInto(*out)
	}
	if in.ReplacePath != nil {
		in, out := &in.ReplacePath, &out.ReplacePath
		*out = new(ReplacePath)
		**out = **in
	}
	if in.ReplacePathRegex != nil {
		in, out := &in.ReplacePathRegex, &out.ReplacePathRegex
		*out = new(ReplacePathRegex)
		**out = **in
	}
	if in.Chain != nil {
		in, out := &in.Chain, &out.Chain
		*out = new(Chain)
		(*in).DeepCopyInto(*out)
	}
	if in.IPWhiteList != nil {
		in, out := &in.IPWhiteList, &out.IPWhiteList
		*out = new(IPWhiteList)
		(*in).DeepCopyInto(*out)
	}
	if in.IPAllowList != nil {
		in, out := &in.IPAllowList, &out.IPAllowList
		*out = new(IPAllowList)
		(*in).DeepCopyInto(*out)
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = new(Headers)
		(*in).DeepCopyInto(*out)
	}
	if in.Errors != nil {
		in, out := &in.Errors, &out.Errors
		*out = new(ErrorPage)
		(*in).DeepCopyInto(*out)
	}
	if in.RateLimit != nil {
		in, out := &in.RateLimit, &out.RateLimit
		*out = new(RateLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.RedirectRegex != nil {
		in, out := &in.RedirectRegex, &out.RedirectRegex
		*out = new(RedirectRegex)
		**out = **in
	}
	if in.RedirectScheme != nil {
		in, out := &in.RedirectScheme, &out.RedirectScheme
		*out = new(RedirectScheme)
		**out = **in
	}
	if in.BasicAuth != nil {
		in, out := &in.BasicAuth, &out.BasicAuth
		*out = new(BasicAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.DigestAuth != nil {
		in, out := &in.DigestAuth, &out.DigestAuth
		*out = new(DigestAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.ForwardAuth != nil {
		in, out := &in.ForwardAuth, &out.ForwardAuth
		*out = new(ForwardAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.InFlightReq != nil {
		in, out := &in.InFlightReq, &out.InFlightReq
		*out = new(InFlightReq)
		(*in).DeepCopyInto(*out)
	}
	if in.Buffering != nil {
		in, out := &in.Buffering, &out.Buffering
		*out = new(Buffering)
		**out = **in
	}
	if in.CircuitBreaker != nil {
		in, out := &in.CircuitBreaker, &out.CircuitBreaker
		*out = new(CircuitBreaker)
		**out = **in
	}
	if in.Compress != nil {
		in, out := &in.Compress, &out.Compress
		*out = new(Compress)
		(*in).DeepCopyInto(*out)
	}
	if in.PassTLSClientCert != nil {
		in, out := &in.PassTLSClientCert, &out.PassTLSClientCert
		*out = new(PassTLSClientCert)
		(*in).DeepCopyInto(*out)
	}
	if in.Retry != nil {
		in, out := &in.Retry, &out.Retry
		*out = new(Retry)
		**out = **in
	}
	if in.ContentType != nil {
		in, out := &in.ContentType, &out.ContentType
		*out = new(ContentType)
		(*in).DeepCopyInto(*out)
	}
	if in.GrpcWeb != nil {
		in, out := &in.GrpcWeb, &out.GrpcWeb
		*out = new(GrpcWeb)
		(*in).DeepCopyInto(*out)
	}
	if in.Plugin != nil {
		in, out := &in.Plugin, &out.Plugin
		*out = make(map[string]PluginConf, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	if in.RequestHeaderModifier != nil {
		in, out := &in.RequestHeaderModifier, &out.RequestHeaderModifier
		*out = new(HeaderModifier)
		(*in).DeepCopyInto(*out)
	}
	if in.ResponseHeaderModifier != nil {
		in, out := &in.ResponseHeaderModifier, &out.ResponseHeaderModifier
		*out = new(HeaderModifier)
		(*in).DeepCopyInto(*out)
	}
	if in.RequestRedirect != nil {
		in, out := &in.RequestRedirect, &out.RequestRedirect
		*out = new(RequestRedirect)
		(*in).DeepCopyInto(*out)
	}
	if in.URLRewrite != nil {
		in, out := &in.URLRewrite, &out.URLRewrite
		*out = new(URLRewrite)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Middleware.
func (in *Middleware) DeepCopy() *Middleware {
	if in == nil {
		return nil
	}
	out := new(Middleware)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MirrorService) DeepCopyInto(out *MirrorService) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MirrorService.
func (in *MirrorService) DeepCopy() *MirrorService {
	if in == nil {
		return nil
	}
	out := new(MirrorService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Mirroring) DeepCopyInto(out *Mirroring) {
	*out = *in
	if in.MirrorBody != nil {
		in, out := &in.MirrorBody, &out.MirrorBody
		*out = new(bool)
		**out = **in
	}
	if in.MaxBodySize != nil {
		in, out := &in.MaxBodySize, &out.MaxBodySize
		*out = new(int64)
		**out = **in
	}
	if in.Mirrors != nil {
		in, out := &in.Mirrors, &out.Mirrors
		*out = make([]MirrorService, len(*in))
		copy(*out, *in)
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheck)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Mirroring.
func (in *Mirroring) DeepCopy() *Mirroring {
	if in == nil {
		return nil
	}
	out := new(Mirroring)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Model) DeepCopyInto(out *Model) {
	*out = *in
	if in.Middlewares != nil {
		in, out := &in.Middlewares, &out.Middlewares
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(RouterTLSConfig)
		(*in).DeepCopyInto(*out)
	}
	in.Observability.DeepCopyInto(&out.Observability)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Model.
func (in *Model) DeepCopy() *Model {
	if in == nil {
		return nil
	}
	out := new(Model)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PassTLSClientCert) DeepCopyInto(out *PassTLSClientCert) {
	*out = *in
	if in.Info != nil {
		in, out := &in.Info, &out.Info
		*out = new(TLSClientCertificateInfo)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PassTLSClientCert.
func (in *PassTLSClientCert) DeepCopy() *PassTLSClientCert {
	if in == nil {
		return nil
	}
	out := new(PassTLSClientCert)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyProtocol) DeepCopyInto(out *ProxyProtocol) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyProtocol.
func (in *ProxyProtocol) DeepCopy() *ProxyProtocol {
	if in == nil {
		return nil
	}
	out := new(ProxyProtocol)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimit) DeepCopyInto(out *RateLimit) {
	*out = *in
	if in.SourceCriterion != nil {
		in, out := &in.SourceCriterion, &out.SourceCriterion
		*out = new(SourceCriterion)
		(*in).DeepCopyInto(*out)
	}
	if in.Redis != nil {
		in, out := &in.Redis, &out.Redis
		*out = new(Redis)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimit.
func (in *RateLimit) DeepCopy() *RateLimit {
	if in == nil {
		return nil
	}
	out := new(RateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RedirectRegex) DeepCopyInto(out *RedirectRegex) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RedirectRegex.
func (in *RedirectRegex) DeepCopy() *RedirectRegex {
	if in == nil {
		return nil
	}
	out := new(RedirectRegex)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RedirectScheme) DeepCopyInto(out *RedirectScheme) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RedirectScheme.
func (in *RedirectScheme) DeepCopy() *RedirectScheme {
	if in == nil {
		return nil
	}
	out := new(RedirectScheme)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Redis) DeepCopyInto(out *Redis) {
	*out = *in
	if in.Endpoints != nil {
		in, out := &in.Endpoints, &out.Endpoints
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(types.ClientTLS)
		**out = **in
	}
	if in.ReadTimeout != nil {
		in, out := &in.ReadTimeout, &out.ReadTimeout
		*out = new(paersertypes.Duration)
		**out = **in
	}
	if in.WriteTimeout != nil {
		in, out := &in.WriteTimeout, &out.WriteTimeout
		*out = new(paersertypes.Duration)
		**out = **in
	}
	if in.DialTimeout != nil {
		in, out := &in.DialTimeout, &out.DialTimeout
		*out = new(paersertypes.Duration)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Redis.
func (in *Redis) DeepCopy() *Redis {
	if in == nil {
		return nil
	}
	out := new(Redis)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ReplacePath) DeepCopyInto(out *ReplacePath) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ReplacePath.
func (in *ReplacePath) DeepCopy() *ReplacePath {
	if in == nil {
		return nil
	}
	out := new(ReplacePath)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ReplacePathRegex) DeepCopyInto(out *ReplacePathRegex) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ReplacePathRegex.
func (in *ReplacePathRegex) DeepCopy() *ReplacePathRegex {
	if in == nil {
		return nil
	}
	out := new(ReplacePathRegex)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RequestRedirect) DeepCopyInto(out *RequestRedirect) {
	*out = *in
	if in.Scheme != nil {
		in, out := &in.Scheme, &out.Scheme
		*out = new(string)
		**out = **in
	}
	if in.Hostname != nil {
		in, out := &in.Hostname, &out.Hostname
		*out = new(string)
		**out = **in
	}
	if in.Port != nil {
		in, out := &in.Port, &out.Port
		*out = new(string)
		**out = **in
	}
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	if in.PathPrefix != nil {
		in, out := &in.PathPrefix, &out.PathPrefix
		*out = new(string)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RequestRedirect.
func (in *RequestRedirect) DeepCopy() *RequestRedirect {
	if in == nil {
		return nil
	}
	out := new(RequestRedirect)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResponseForwarding) DeepCopyInto(out *ResponseForwarding) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResponseForwarding.
func (in *ResponseForwarding) DeepCopy() *ResponseForwarding {
	if in == nil {
		return nil
	}
	out := new(ResponseForwarding)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Retry) DeepCopyInto(out *Retry) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Retry.
func (in *Retry) DeepCopy() *Retry {
	if in == nil {
		return nil
	}
	out := new(Retry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Router) DeepCopyInto(out *Router) {
	*out = *in
	if in.EntryPoints != nil {
		in, out := &in.EntryPoints, &out.EntryPoints
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Middlewares != nil {
		in, out := &in.Middlewares, &out.Middlewares
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(RouterTLSConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Observability != nil {
		in, out := &in.Observability, &out.Observability
		*out = new(RouterObservabilityConfig)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Router.
func (in *Router) DeepCopy() *Router {
	if in == nil {
		return nil
	}
	out := new(Router)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RouterObservabilityConfig) DeepCopyInto(out *RouterObservabilityConfig) {
	*out = *in
	if in.AccessLogs != nil {
		in, out := &in.AccessLogs, &out.AccessLogs
		*out = new(bool)
		**out = **in
	}
	if in.Tracing != nil {
		in, out := &in.Tracing, &out.Tracing
		*out = new(bool)
		**out = **in
	}
	if in.Metrics != nil {
		in, out := &in.Metrics, &out.Metrics
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RouterObservabilityConfig.
func (in *RouterObservabilityConfig) DeepCopy() *RouterObservabilityConfig {
	if in == nil {
		return nil
	}
	out := new(RouterObservabilityConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RouterTCPTLSConfig) DeepCopyInto(out *RouterTCPTLSConfig) {
	*out = *in
	if in.Domains != nil {
		in, out := &in.Domains, &out.Domains
		*out = make([]types.Domain, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RouterTCPTLSConfig.
func (in *RouterTCPTLSConfig) DeepCopy() *RouterTCPTLSConfig {
	if in == nil {
		return nil
	}
	out := new(RouterTCPTLSConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RouterTLSConfig) DeepCopyInto(out *RouterTLSConfig) {
	*out = *in
	if in.Domains != nil {
		in, out := &in.Domains, &out.Domains
		*out = make([]types.Domain, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RouterTLSConfig.
func (in *RouterTLSConfig) DeepCopy() *RouterTLSConfig {
	if in == nil {
		return nil
	}
	out := new(RouterTLSConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Server) DeepCopyInto(out *Server) {
	*out = *in
	if in.Weight != nil {
		in, out := &in.Weight, &out.Weight
		*out = new(int)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Server.
func (in *Server) DeepCopy() *Server {
	if in == nil {
		return nil
	}
	out := new(Server)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServerHealthCheck) DeepCopyInto(out *ServerHealthCheck) {
	*out = *in
	if in.UnhealthyInterval != nil {
		in, out := &in.UnhealthyInterval, &out.UnhealthyInterval
		*out = new(paersertypes.Duration)
		**out = **in
	}
	if in.FollowRedirects != nil {
		in, out := &in.FollowRedirects, &out.FollowRedirects
		*out = new(bool)
		**out = **in
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServerHealthCheck.
func (in *ServerHealthCheck) DeepCopy() *ServerHealthCheck {
	if in == nil {
		return nil
	}
	out := new(ServerHealthCheck)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServersLoadBalancer) DeepCopyInto(out *ServersLoadBalancer) {
	*out = *in
	if in.Sticky != nil {
		in, out := &in.Sticky, &out.Sticky
		*out = new(Sticky)
		(*in).DeepCopyInto(*out)
	}
	if in.Servers != nil {
		in, out := &in.Servers, &out.Servers
		*out = make([]Server, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(ServerHealthCheck)
		(*in).DeepCopyInto(*out)
	}
	if in.PassHostHeader != nil {
		in, out := &in.PassHostHeader, &out.PassHostHeader
		*out = new(bool)
		**out = **in
	}
	if in.ResponseForwarding != nil {
		in, out := &in.ResponseForwarding, &out.ResponseForwarding
		*out = new(ResponseForwarding)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServersLoadBalancer.
func (in *ServersLoadBalancer) DeepCopy() *ServersLoadBalancer {
	if in == nil {
		return nil
	}
	out := new(ServersLoadBalancer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServersTransport) DeepCopyInto(out *ServersTransport) {
	*out = *in
	if in.RootCAs != nil {
		in, out := &in.RootCAs, &out.RootCAs
		*out = make([]types.FileOrContent, len(*in))
		copy(*out, *in)
	}
	if in.Certificates != nil {
		in, out := &in.Certificates, &out.Certificates
		*out = make(tls.Certificates, len(*in))
		copy(*out, *in)
	}
	if in.ForwardingTimeouts != nil {
		in, out := &in.ForwardingTimeouts, &out.ForwardingTimeouts
		*out = new(ForwardingTimeouts)
		**out = **in
	}
	if in.Spiffe != nil {
		in, out := &in.Spiffe, &out.Spiffe
		*out = new(Spiffe)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServersTransport.
func (in *ServersTransport) DeepCopy() *ServersTransport {
	if in == nil {
		return nil
	}
	out := new(ServersTransport)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Service) DeepCopyInto(out *Service) {
	*out = *in
	if in.LoadBalancer != nil {
		in, out := &in.LoadBalancer, &out.LoadBalancer
		*out = new(ServersLoadBalancer)
		(*in).DeepCopyInto(*out)
	}
	if in.Weighted != nil {
		in, out := &in.Weighted, &out.Weighted
		*out = new(WeightedRoundRobin)
		(*in).DeepCopyInto(*out)
	}
	if in.Mirroring != nil {
		in, out := &in.Mirroring, &out.Mirroring
		*out = new(Mirroring)
		(*in).DeepCopyInto(*out)
	}
	if in.Failover != nil {
		in, out := &in.Failover, &out.Failover
		*out = new(Failover)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Service.
func (in *Service) DeepCopy() *Service {
	if in == nil {
		return nil
	}
	out := new(Service)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SourceCriterion) DeepCopyInto(out *SourceCriterion) {
	*out = *in
	if in.IPStrategy != nil {
		in, out := &in.IPStrategy, &out.IPStrategy
		*out = new(IPStrategy)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SourceCriterion.
func (in *SourceCriterion) DeepCopy() *SourceCriterion {
	if in == nil {
		return nil
	}
	out := new(SourceCriterion)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Spiffe) DeepCopyInto(out *Spiffe) {
	*out = *in
	if in.IDs != nil {
		in, out := &in.IDs, &out.IDs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Spiffe.
func (in *Spiffe) DeepCopy() *Spiffe {
	if in == nil {
		return nil
	}
	out := new(Spiffe)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Sticky) DeepCopyInto(out *Sticky) {
	*out = *in
	if in.Cookie != nil {
		in, out := &in.Cookie, &out.Cookie
		*out = new(Cookie)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Sticky.
func (in *Sticky) DeepCopy() *Sticky {
	if in == nil {
		return nil
	}
	out := new(Sticky)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StripPrefix) DeepCopyInto(out *StripPrefix) {
	*out = *in
	if in.Prefixes != nil {
		in, out := &in.Prefixes, &out.Prefixes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ForceSlash != nil {
		in, out := &in.ForceSlash, &out.ForceSlash
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StripPrefix.
func (in *StripPrefix) DeepCopy() *StripPrefix {
	if in == nil {
		return nil
	}
	out := new(StripPrefix)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StripPrefixRegex) DeepCopyInto(out *StripPrefixRegex) {
	*out = *in
	if in.Regex != nil {
		in, out := &in.Regex, &out.Regex
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StripPrefixRegex.
func (in *StripPrefixRegex) DeepCopy() *StripPrefixRegex {
	if in == nil {
		return nil
	}
	out := new(StripPrefixRegex)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPConfiguration) DeepCopyInto(out *TCPConfiguration) {
	*out = *in
	if in.Routers != nil {
		in, out := &in.Routers, &out.Routers
		*out = make(map[string]*TCPRouter, len(*in))
		for key, val := range *in {
			var outVal *TCPRouter
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(TCPRouter)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make(map[string]*TCPService, len(*in))
		for key, val := range *in {
			var outVal *TCPService
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(TCPService)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.Middlewares != nil {
		in, out := &in.Middlewares, &out.Middlewares
		*out = make(map[string]*TCPMiddleware, len(*in))
		for key, val := range *in {
			var outVal *TCPMiddleware
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(TCPMiddleware)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.Models != nil {
		in, out := &in.Models, &out.Models
		*out = make(map[string]*TCPModel, len(*in))
		for key, val := range *in {
			var outVal *TCPModel
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(TCPModel)
				**out = **in
			}
			(*out)[key] = outVal
		}
	}
	if in.ServersTransports != nil {
		in, out := &in.ServersTransports, &out.ServersTransports
		*out = make(map[string]*TCPServersTransport, len(*in))
		for key, val := range *in {
			var outVal *TCPServersTransport
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(TCPServersTransport)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPConfiguration.
func (in *TCPConfiguration) DeepCopy() *TCPConfiguration {
	if in == nil {
		return nil
	}
	out := new(TCPConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPIPAllowList) DeepCopyInto(out *TCPIPAllowList) {
	*out = *in
	if in.SourceRange != nil {
		in, out := &in.SourceRange, &out.SourceRange
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPIPAllowList.
func (in *TCPIPAllowList) DeepCopy() *TCPIPAllowList {
	if in == nil {
		return nil
	}
	out := new(TCPIPAllowList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPIPWhiteList) DeepCopyInto(out *TCPIPWhiteList) {
	*out = *in
	if in.SourceRange != nil {
		in, out := &in.SourceRange, &out.SourceRange
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPIPWhiteList.
func (in *TCPIPWhiteList) DeepCopy() *TCPIPWhiteList {
	if in == nil {
		return nil
	}
	out := new(TCPIPWhiteList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPInFlightConn) DeepCopyInto(out *TCPInFlightConn) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPInFlightConn.
func (in *TCPInFlightConn) DeepCopy() *TCPInFlightConn {
	if in == nil {
		return nil
	}
	out := new(TCPInFlightConn)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPMiddleware) DeepCopyInto(out *TCPMiddleware) {
	*out = *in
	if in.InFlightConn != nil {
		in, out := &in.InFlightConn, &out.InFlightConn
		*out = new(TCPInFlightConn)
		**out = **in
	}
	if in.IPWhiteList != nil {
		in, out := &in.IPWhiteList, &out.IPWhiteList
		*out = new(TCPIPWhiteList)
		(*in).DeepCopyInto(*out)
	}
	if in.IPAllowList != nil {
		in, out := &in.IPAllowList, &out.IPAllowList
		*out = new(TCPIPAllowList)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPMiddleware.
func (in *TCPMiddleware) DeepCopy() *TCPMiddleware {
	if in == nil {
		return nil
	}
	out := new(TCPMiddleware)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPModel) DeepCopyInto(out *TCPModel) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPModel.
func (in *TCPModel) DeepCopy() *TCPModel {
	if in == nil {
		return nil
	}
	out := new(TCPModel)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPRouter) DeepCopyInto(out *TCPRouter) {
	*out = *in
	if in.EntryPoints != nil {
		in, out := &in.EntryPoints, &out.EntryPoints
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Middlewares != nil {
		in, out := &in.Middlewares, &out.Middlewares
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(RouterTCPTLSConfig)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPRouter.
func (in *TCPRouter) DeepCopy() *TCPRouter {
	if in == nil {
		return nil
	}
	out := new(TCPRouter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPServer) DeepCopyInto(out *TCPServer) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPServer.
func (in *TCPServer) DeepCopy() *TCPServer {
	if in == nil {
		return nil
	}
	out := new(TCPServer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPServersLoadBalancer) DeepCopyInto(out *TCPServersLoadBalancer) {
	*out = *in
	if in.ProxyProtocol != nil {
		in, out := &in.ProxyProtocol, &out.ProxyProtocol
		*out = new(ProxyProtocol)
		**out = **in
	}
	if in.Servers != nil {
		in, out := &in.Servers, &out.Servers
		*out = make([]TCPServer, len(*in))
		copy(*out, *in)
	}
	if in.TerminationDelay != nil {
		in, out := &in.TerminationDelay, &out.TerminationDelay
		*out = new(int)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPServersLoadBalancer.
func (in *TCPServersLoadBalancer) DeepCopy() *TCPServersLoadBalancer {
	if in == nil {
		return nil
	}
	out := new(TCPServersLoadBalancer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPServersTransport) DeepCopyInto(out *TCPServersTransport) {
	*out = *in
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(TLSClientConfig)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPServersTransport.
func (in *TCPServersTransport) DeepCopy() *TCPServersTransport {
	if in == nil {
		return nil
	}
	out := new(TCPServersTransport)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPService) DeepCopyInto(out *TCPService) {
	*out = *in
	if in.LoadBalancer != nil {
		in, out := &in.LoadBalancer, &out.LoadBalancer
		*out = new(TCPServersLoadBalancer)
		(*in).DeepCopyInto(*out)
	}
	if in.Weighted != nil {
		in, out := &in.Weighted, &out.Weighted
		*out = new(TCPWeightedRoundRobin)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPService.
func (in *TCPService) DeepCopy() *TCPService {
	if in == nil {
		return nil
	}
	out := new(TCPService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPWRRService) DeepCopyInto(out *TCPWRRService) {
	*out = *in
	if in.Weight != nil {
		in, out := &in.Weight, &out.Weight
		*out = new(int)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPWRRService.
func (in *TCPWRRService) DeepCopy() *TCPWRRService {
	if in == nil {
		return nil
	}
	out := new(TCPWRRService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPWeightedRoundRobin) DeepCopyInto(out *TCPWeightedRoundRobin) {
	*out = *in
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make([]TCPWRRService, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPWeightedRoundRobin.
func (in *TCPWeightedRoundRobin) DeepCopy() *TCPWeightedRoundRobin {
	if in == nil {
		return nil
	}
	out := new(TCPWeightedRoundRobin)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSClientCertificateInfo) DeepCopyInto(out *TLSClientCertificateInfo) {
	*out = *in
	if in.Subject != nil {
		in, out := &in.Subject, &out.Subject
		*out = new(TLSClientCertificateSubjectDNInfo)
		**out = **in
	}
	if in.Issuer != nil {
		in, out := &in.Issuer, &out.Issuer
		*out = new(TLSClientCertificateIssuerDNInfo)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSClientCertificateInfo.
func (in *TLSClientCertificateInfo) DeepCopy() *TLSClientCertificateInfo {
	if in == nil {
		return nil
	}
	out := new(TLSClientCertificateInfo)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSClientCertificateIssuerDNInfo) DeepCopyInto(out *TLSClientCertificateIssuerDNInfo) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSClientCertificateIssuerDNInfo.
func (in *TLSClientCertificateIssuerDNInfo) DeepCopy() *TLSClientCertificateIssuerDNInfo {
	if in == nil {
		return nil
	}
	out := new(TLSClientCertificateIssuerDNInfo)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSClientCertificateSubjectDNInfo) DeepCopyInto(out *TLSClientCertificateSubjectDNInfo) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSClientCertificateSubjectDNInfo.
func (in *TLSClientCertificateSubjectDNInfo) DeepCopy() *TLSClientCertificateSubjectDNInfo {
	if in == nil {
		return nil
	}
	out := new(TLSClientCertificateSubjectDNInfo)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSClientConfig) DeepCopyInto(out *TLSClientConfig) {
	*out = *in
	if in.RootCAs != nil {
		in, out := &in.RootCAs, &out.RootCAs
		*out = make([]types.FileOrContent, len(*in))
		copy(*out, *in)
	}
	if in.Certificates != nil {
		in, out := &in.Certificates, &out.Certificates
		*out = make(tls.Certificates, len(*in))
		copy(*out, *in)
	}
	if in.Spiffe != nil {
		in, out := &in.Spiffe, &out.Spiffe
		*out = new(Spiffe)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSClientConfig.
func (in *TLSClientConfig) DeepCopy() *TLSClientConfig {
	if in == nil {
		return nil
	}
	out := new(TLSClientConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSConfiguration) DeepCopyInto(out *TLSConfiguration) {
	*out = *in
	if in.Certificates != nil {
		in, out := &in.Certificates, &out.Certificates
		*out = make([]*tls.CertAndStores, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(tls.CertAndStores)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Options != nil {
		in, out := &in.Options, &out.Options
		*out = make(map[string]tls.Options, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	if in.Stores != nil {
		in, out := &in.Stores, &out.Stores
		*out = make(map[string]tls.Store, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSConfiguration.
func (in *TLSConfiguration) DeepCopy() *TLSConfiguration {
	if in == nil {
		return nil
	}
	out := new(TLSConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPConfiguration) DeepCopyInto(out *UDPConfiguration) {
	*out = *in
	if in.Routers != nil {
		in, out := &in.Routers, &out.Routers
		*out = make(map[string]*UDPRouter, len(*in))
		for key, val := range *in {
			var outVal *UDPRouter
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(UDPRouter)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make(map[string]*UDPService, len(*in))
		for key, val := range *in {
			var outVal *UDPService
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = new(UDPService)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPConfiguration.
func (in *UDPConfiguration) DeepCopy() *UDPConfiguration {
	if in == nil {
		return nil
	}
	out := new(UDPConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPRouter) DeepCopyInto(out *UDPRouter) {
	*out = *in
	if in.EntryPoints != nil {
		in, out := &in.EntryPoints, &out.EntryPoints
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPRouter.
func (in *UDPRouter) DeepCopy() *UDPRouter {
	if in == nil {
		return nil
	}
	out := new(UDPRouter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPServer) DeepCopyInto(out *UDPServer) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPServer.
func (in *UDPServer) DeepCopy() *UDPServer {
	if in == nil {
		return nil
	}
	out := new(UDPServer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPServersLoadBalancer) DeepCopyInto(out *UDPServersLoadBalancer) {
	*out = *in
	if in.Servers != nil {
		in, out := &in.Servers, &out.Servers
		*out = make([]UDPServer, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPServersLoadBalancer.
func (in *UDPServersLoadBalancer) DeepCopy() *UDPServersLoadBalancer {
	if in == nil {
		return nil
	}
	out := new(UDPServersLoadBalancer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPService) DeepCopyInto(out *UDPService) {
	*out = *in
	if in.LoadBalancer != nil {
		in, out := &in.LoadBalancer, &out.LoadBalancer
		*out = new(UDPServersLoadBalancer)
		(*in).DeepCopyInto(*out)
	}
	if in.Weighted != nil {
		in, out := &in.Weighted, &out.Weighted
		*out = new(UDPWeightedRoundRobin)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPService.
func (in *UDPService) DeepCopy() *UDPService {
	if in == nil {
		return nil
	}
	out := new(UDPService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPWRRService) DeepCopyInto(out *UDPWRRService) {
	*out = *in
	if in.Weight != nil {
		in, out := &in.Weight, &out.Weight
		*out = new(int)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPWRRService.
func (in *UDPWRRService) DeepCopy() *UDPWRRService {
	if in == nil {
		return nil
	}
	out := new(UDPWRRService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPWeightedRoundRobin) DeepCopyInto(out *UDPWeightedRoundRobin) {
	*out = *in
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make([]UDPWRRService, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPWeightedRoundRobin.
func (in *UDPWeightedRoundRobin) DeepCopy() *UDPWeightedRoundRobin {
	if in == nil {
		return nil
	}
	out := new(UDPWeightedRoundRobin)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *URLRewrite) DeepCopyInto(out *URLRewrite) {
	*out = *in
	if in.Hostname != nil {
		in, out := &in.Hostname, &out.Hostname
		*out = new(string)
		**out = **in
	}
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	if in.PathPrefix != nil {
		in, out := &in.PathPrefix, &out.PathPrefix
		*out = new(string)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new URLRewrite.
func (in *URLRewrite) DeepCopy() *URLRewrite {
	if in == nil {
		return nil
	}
	out := new(URLRewrite)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in Users) DeepCopyInto(out *Users) {
	{
		in := &in
		*out = make(Users, len(*in))
		copy(*out, *in)
		return
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Users.
func (in Users) DeepCopy() Users {
	if in == nil {
		return nil
	}
	out := new(Users)
	in.DeepCopyInto(out)
	return *out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WRRService) DeepCopyInto(out *WRRService) {
	*out = *in
	if in.Weight != nil {
		in, out := &in.Weight, &out.Weight
		*out = new(int)
		**out = **in
	}
	if in.Status != nil {
		in, out := &in.Status, &out.Status
		*out = new(int)
		**out = **in
	}
	if in.GRPCStatus != nil {
		in, out := &in.GRPCStatus, &out.GRPCStatus
		*out = new(GRPCStatus)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WRRService.
func (in *WRRService) DeepCopy() *WRRService {
	if in == nil {
		return nil
	}
	out := new(WRRService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WeightedRoundRobin) DeepCopyInto(out *WeightedRoundRobin) {
	*out = *in
	if in.Services != nil {
		in, out := &in.Services, &out.Services
		*out = make([]WRRService, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Sticky != nil {
		in, out := &in.Sticky, &out.Sticky
		*out = new(Sticky)
		(*in).DeepCopyInto(*out)
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheck)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WeightedRoundRobin.
func (in *WeightedRoundRobin) DeepCopy() *WeightedRoundRobin {
	if in == nil {
		return nil
	}
	out := new(WeightedRoundRobin)
	in.DeepCopyInto(out)
	return out
}
