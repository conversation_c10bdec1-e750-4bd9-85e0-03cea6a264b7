# This file is not meant as a usable Traefik configuration.
# It is only used in tests as a sample for serialization.
[global]
  checkNewVersion = true
  sendAnonymousUsage = true

[serversTransport]
  insecureSkipVerify = true
  rootCAs = ["foobar", "foobar"]
  maxIdleConnsPerHost = 42
  [serversTransport.forwardingTimeouts]
    dialTimeout = 42
    responseHeaderTimeout = 42
    idleConnTimeout = 42

[entryPoints]
  [entryPoints.EntryPoint0]
    address = "foobar"
    [entryPoints.EntryPoint0.transport]
      [entryPoints.EntryPoint0.transport.lifeCycle]
        requestAcceptGraceTimeout = 42
        graceTimeOut = 42
      [entryPoints.EntryPoint0.transport.respondingTimeouts]
        readTimeout = 42
        writeTimeout = 42
        idleTimeout = 42
    [entryPoints.EntryPoint0.proxyProtocol]
      insecure = true
      trustedIPs = ["foobar", "foobar"]
    [entryPoints.EntryPoint0.forwardedHeaders]
      insecure = true
      trustedIPs = ["foobar", "foobar"]

[providers]
  providersThrottleDuration = 42
  [providers.docker]
    constraints = "foobar"
    watch = true
    endpoint = "foobar"
    defaultRule = "foobar"
    exposedByDefault = true
    useBindPortIP = true
    network = "foobar"
    httpClientTimeout = 42
    [providers.docker.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
  [providers.file]
    directory = "foobar"
    watch = true
    filename = "foobar"
    debugLogGeneratedTemplate = true
  [providers.kubernetesIngress]
    endpoint = "foobar"
    token = "foobar"
    certAuthFilePath = "foobar"
    namespaces = ["foobar", "foobar"]
    labelSelector = "foobar"
    ingressClass = "foobar"
    [providers.kubernetesIngress.ingressEndpoint]
      ip = "foobar"
      hostname = "foobar"
      publishedService = "foobar"
  [providers.kubernetesCRD]
    endpoint = "foobar"
    token = "foobar"
    certAuthFilePath = "foobar"
    namespaces = ["foobar", "foobar"]
    labelSelector = "foobar"
    ingressClass = "foobar"
  [providers.rest]
    entryPoint = "foobar"

[api]
  entryPoint = "foobar"
  dashboard = true
  middlewares = ["foobar", "foobar"]
  [api.statistics]
    recentErrors = 42

[metrics]
  [metrics.prometheus]
    buckets = [42.0, 42.0]
    entryPoint = "foobar"
    middlewares = ["foobar", "foobar"]
  [metrics.datadog]
    address = "foobar"
    pushInterval = "10s"
  [metrics.statsD]
    address = "foobar"
    pushInterval = "10s"

[ping]
  entryPoint = "foobar"
  middlewares = ["foobar", "foobar"]

[log]
  level = "foobar"
  filePath = "foobar"
  format = "foobar"

[accessLog]
  filePath = "foobar"
  format = "foobar"
  bufferingSize = 42
  [accessLog.filters]
    statusCodes = ["foobar", "foobar"]
    retryAttempts = true
    minDuration = 42
  [accessLog.fields]
    defaultMode = "foobar"
    [accessLog.fields.names]
      name0 = "foobar"
      name1 = "foobar"
    [accessLog.fields.headers]
      defaultMode = "foobar"
      [accessLog.fields.headers.names]
        name0 = "foobar"
        name1 = "foobar"

[tracing]
  serviceName = "foobar"
  sampleRate = 42
  [tracing.headers]
    header1 = "foobar"
    header2 = "foobar"
  [tracing.globalAttributes]
    attr1 = "foobar"
    attr2 = "foobar"
  [tracing.otlp.grpc]
    endpoint = "foobar"
    insecure = true
    [tracing.otlp.grpc.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true
  [tracing.otlp.http]
    endpoint = "foobar"
    insecure = true
    [tracing.otlp.http.tls]
      ca = "foobar"
      cert = "foobar"
      key = "foobar"
      insecureSkipVerify = true

[hostResolver]
  cnameFlattening = true
  resolvConfig = "foobar"
  resolvDepth = 42

[acme]
  email = "foobar"
  acmeLogging = true
  caServer = "foobar"
  storage = "foobar"
  entryPoint = "foobar"
  keyType = "foobar"
  [acme.dnsChallenge]
    provider = "foobar"
    delayBeforeCheck = 42
    resolvers = ["foobar", "foobar"]
    disablePropagationCheck = true
  [acme.httpChallenge]
    entryPoint = "foobar"
  [acme.tlsChallenge]

  [[acme.domains]]
    main = "foobar"
    sans = ["foobar", "foobar"]

  [[acme.domains]]
    main = "foobar"
    sans = ["foobar", "foobar"]

## Dynamic configuration

[http]
  [http.routers]
    [http.routers.Router0]
      entryPoints = ["foobar", "foobar"]
      middlewares = ["foobar", "foobar"]
      service = "foobar"
      rule = "foobar"
      priority = 42
      [http.routers.Router0.tls]
  [http.middlewares]
    [http.middlewares.Middleware0]
      [http.middlewares.Middleware0.addPrefix]
        prefix = "foobar"
    [http.middlewares.Middleware1]
      [http.middlewares.Middleware1.stripPrefix]
        prefixes = ["foobar", "foobar"]
    [http.middlewares.Middleware10]
      [http.middlewares.Middleware10.rateLimit]
        average = 42
        period = "1s"
        burst = 42
        [http.middlewares.Middleware10.rateLimit.sourceCriterion]
          requestHeaderName = "foobar"
          requestHost = true
        [http.middlewares.Middleware10.rateLimit.sourceCriterion.ipStrategy]
          depth = 42
          excludedIPs = ["foobar", "foobar"]
    [http.middlewares.Middleware11]
      [http.middlewares.Middleware11.redirectRegex]
        regex = "foobar"
        replacement = "foobar"
        permanent = true
    [http.middlewares.Middleware12]
      [http.middlewares.Middleware12.redirectScheme]
        scheme = "foobar"
        port = "foobar"
        permanent = true
    [http.middlewares.Middleware13]
      [http.middlewares.Middleware13.basicAuth]
        users = ["foobar", "foobar"]
        usersFile = "foobar"
        realm = "foobar"
        removeHeader = true
        headerField = "foobar"
    [http.middlewares.Middleware14]
      [http.middlewares.Middleware14.digestAuth]
        users = ["foobar", "foobar"]
        usersFile = "foobar"
        removeHeader = true
        realm = "foobar"
        headerField = "foobar"
    [http.middlewares.Middleware15]
      [http.middlewares.Middleware15.forwardAuth]
        address = "foobar"
        trustForwardHeader = true
        authResponseHeaders = ["foobar", "foobar"]
        authResponseHeadersRegex = "foobar"
        authRequestHeaders = ["foobar", "foobar"]
        [http.middlewares.Middleware15.forwardAuth.tls]
          ca = "foobar"
          cert = "foobar"
          key = "foobar"
          insecureSkipVerify = true
    [http.middlewares.Middleware16]
      [http.middlewares.Middleware16.inFlightReq]
        amount = 42
        [http.middlewares.Middleware16.inFlightReq.sourceCriterion]
          requestHeaderName = "foobar"
          requestHost = true
          [http.middlewares.Middleware16.inFlightReq.sourceCriterion.ipStrategy]
            depth = 42
            excludedIPs = ["foobar", "foobar"]
    [http.middlewares.Middleware17]
      [http.middlewares.Middleware17.buffering]
        maxRequestBodyBytes = 42
        memRequestBodyBytes = 42
        maxResponseBodyBytes = 42
        memResponseBodyBytes = 42
        retryExpression = "foobar"
    [http.middlewares.Middleware18]
      [http.middlewares.Middleware18.circuitBreaker]
        expression = "foobar"
    [http.middlewares.Middleware19]
      [http.middlewares.Middleware19.compress]
    [http.middlewares.Middleware2]
      [http.middlewares.Middleware2.stripPrefixRegex]
        regex = ["foobar", "foobar"]
    [http.middlewares.Middleware20]
      [http.middlewares.Middleware20.passTLSClientCert]
        pem = true
        [http.middlewares.Middleware20.passTLSClientCert.info]
          notAfter = true
          notBefore = true
          sans = true
          [http.middlewares.Middleware20.passTLSClientCert.info.subject]
            country = true
            province = true
            locality = true
            organization = true
            organizationalUnit = true
            commonName = true
            serialNumber = true
            domainComponent = true
          [http.middlewares.Middleware20.passTLSClientCert.info.issuer]
            country = true
            province = true
            locality = true
            organization = true
            commonName = true
            serialNumber = true
            domainComponent = true
    [http.middlewares.Middleware21]
      [http.middlewares.Middleware21.retry]
        regex = 0
    [http.middlewares.Middleware3]
      [http.middlewares.Middleware3.replacePath]
        path = "foobar"
    [http.middlewares.Middleware4]
      [http.middlewares.Middleware4.replacePathRegex]
        regex = "foobar"
        replacement = "foobar"
    [http.middlewares.Middleware5]
      [http.middlewares.Middleware5.chain]
        middlewares = ["foobar", "foobar"]
    [http.middlewares.Middleware6]
      [http.middlewares.Middleware6.ipAllowList]
        sourceRange = ["foobar", "foobar"]
    [http.middlewares.Middleware7]
      [http.middlewares.Middleware7.ipAllowList]
        [http.middlewares.Middleware7.ipAllowList.ipStrategy]
          depth = 42
          excludedIPs = ["foobar", "foobar"]
    [http.middlewares.Middleware8]
      [http.middlewares.Middleware8.headers]
        accessControlAllowCredentials = true
        accessControlAllowHeaders = ["foobar", "foobar"]
        accessControlAllowMethods = ["foobar", "foobar"]
        accessControlAllowOriginList = ["foobar", "foobar"]
        accessControlExposeHeaders = ["foobar", "foobar"]
        accessControlMaxAge = 42
        addVaryHeader = true
        allowedHosts = ["foobar", "foobar"]
        hostsProxyHeaders = ["foobar", "foobar"]
        stsSeconds = 42
        stsIncludeSubdomains = true
        stsPreload = true
        forceSTSHeader = true
        frameDeny = true
        customFrameOptionsValue = "foobar"
        contentTypeNosniff = true
        browserXssFilter = true
        customBrowserXSSValue = "foobar"
        contentSecurityPolicy = "foobar"
        contentSecurityPolicyReportOnly = "foobar"
        publicKey = "foobar"
        referrerPolicy = "foobar"
        isDevelopment = true
        [http.middlewares.Middleware8.headers.customRequestHeaders]
          name0 = "foobar"
          name1 = "foobar"
        [http.middlewares.Middleware8.headers.customResponseHeaders]
          name0 = "foobar"
          name1 = "foobar"
        [http.middlewares.Middleware8.headers.sslProxyHeaders]
          name0 = "foobar"
          name1 = "foobar"
    [http.middlewares.Middleware9]
      [http.middlewares.Middleware9.errors]
        status = ["foobar", "foobar"]
        service = "foobar"
        query = "foobar"
  [http.services]
    [http.services.Service0]
      [http.services.Service0.loadBalancer]
        passHostHeader = true
        [http.services.Service0.loadBalancer.sticky.cookie]
          name = "foobar"

        [[http.services.Service0.loadBalancer.servers]]
          url = "foobar"

        [[http.services.Service0.loadBalancer.servers]]
          url = "foobar"
        [http.services.Service0.loadBalancer.healthCheck]
          scheme = "foobar"
          mode = "foobar"
          path = "foobar"
          port = 42
          interval = "10s"
          timeout = "10s"
          hostname = "foobar"
          [http.services.Service0.loadBalancer.healthCheck.headers]
            name0 = "foobar"
            name1 = "foobar"
        [http.services.Service0.loadBalancer.responseForwarding]
          flushInterval = "10s"

[tcp]
  [tcp.routers]
    [tcp.routers.TCPRouter0]
      entryPoints = ["foobar", "foobar"]
      service = "foobar"
      rule = "foobar"
      [tcp.routers.TCPRouter0.tls]
        passthrough = true
  [tcp.services]
    [tcp.services.TCPService0]
      [tcp.services.TCPService0.loadBalancer]

        [[tcp.services.TCPService0.loadBalancer.servers]]
          address = "foobar"

        [[tcp.services.TCPService0.loadBalancer.servers]]
          address = "foobar"

[tls]

  [[tls.Certificates]]
    certFile = "foobar"
    keyFile = "foobar"
    stores = ["foobar", "foobar"]

  [[tls.Certificates]]
    certFile = "foobar"
    keyFile = "foobar"
    stores = ["foobar", "foobar"]
  [tls.options]
    [tls.options.TLS0]
      minVersion = "foobar"
      cipherSuites = ["foobar", "foobar"]
      sniStrict = true
      [tls.options.TLS0.clientCA]
        files = ["foobar", "foobar"]
        optional = true
    [tls.options.TLS1]
      minVersion = "foobar"
      cipherSuites = ["foobar", "foobar"]
      sniStrict = true
      [tls.options.TLS1.clientCA]
        files = ["foobar", "foobar"]
        optional = true
  [tls.stores]
    [tls.stores.Store0]
      [tls.stores.Store0.defaultCertificate]
        certFile = "foobar"
        keyFile = "foobar"
    [tls.stores.Store1]
      [tls.stores.Store1.defaultCertificate]
        certFile = "foobar"
        keyFile = "foobar"
