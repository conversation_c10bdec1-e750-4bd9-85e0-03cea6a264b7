{"features": {"accessLog": false, "metrics": "", "tracing": ""}, "http": {"middlewares": {"errors": 0, "total": 0, "warnings": 0}, "routers": {"errors": 0, "total": 0, "warnings": 0}, "services": {"errors": 0, "total": 0, "warnings": 0}}, "tcp": {"middlewares": {"errors": 0, "total": 0, "warnings": 0}, "routers": {"errors": 0, "total": 0, "warnings": 0}, "services": {"errors": 0, "total": 0, "warnings": 0}}, "udp": {"routers": {"errors": 0, "total": 0, "warnings": 0}, "services": {"errors": 0, "total": 0, "warnings": 0}}}