[{"entryPoints": ["web"], "middlewares": ["auth", "addPrefixTest@anotherprovider"], "name": "bar@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`foo.bar`)", "service": "foo-service@myprovider", "status": "enabled", "using": ["web"]}, {"entryPoints": ["web"], "middlewares": ["addPrefixTest", "auth"], "name": "test@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`foo.bar.other`)", "service": "foo-service@myprovider", "status": "enabled", "using": ["web"]}]