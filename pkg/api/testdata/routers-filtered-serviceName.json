[{"entryPoints": ["web"], "name": "foo@otherprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`fii.foo.other`)", "service": "fii-service", "status": "enabled", "using": ["web"]}, {"entryPoints": ["web"], "middlewares": ["addPrefixTest", "auth"], "name": "test@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`fii.bar.other`)", "service": "fii-service@myprovider", "status": "enabled", "using": ["web"]}]