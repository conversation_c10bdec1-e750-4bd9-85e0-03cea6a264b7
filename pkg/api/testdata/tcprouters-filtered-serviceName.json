[{"entryPoints": ["web"], "name": "bar@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`foo.bar`)", "service": "foo-service", "status": "warning", "using": ["web"]}, {"entryPoints": ["web"], "name": "test@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`foo.bar.other`)", "service": "foo-service@myprovider", "status": "enabled", "using": ["web"], "tls": {"passthrough": false}}]