[{"address": ":80", "forwardedHeaders": {"insecure": true, "trustedIPs": ["***********", "***********"]}, "http": {}, "name": "web", "proxyProtocol": {"insecure": true, "trustedIPs": ["***********", "***********"]}, "transport": {"lifeCycle": {"graceTimeOut": "2ns", "requestAcceptGraceTimeout": "1ns"}, "respondingTimeouts": {"idleTimeout": "5ns", "readTimeout": "3ns", "writeTimeout": "4ns"}}}, {"address": ":443", "forwardedHeaders": {"insecure": true, "trustedIPs": ["***********0", "************"]}, "http": {}, "name": "websecure", "proxyProtocol": {"insecure": true, "trustedIPs": ["************", "************"]}, "transport": {"lifeCycle": {"graceTimeOut": "20ns", "requestAcceptGraceTimeout": "10ns"}, "respondingTimeouts": {"idleTimeout": "50ns", "readTimeout": "30ns", "writeTimeout": "40ns"}}}]