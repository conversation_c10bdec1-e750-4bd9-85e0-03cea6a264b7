[{"entryPoints": ["web"], "name": "bar@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`foo.bar`)", "service": "foo-service@myprovider", "status": "warning", "using": ["web"]}, {"entryPoints": ["web"], "name": "foo@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`foo.bar`)", "service": "foo-service@myprovider", "status": "disabled", "using": ["web"]}, {"entryPoints": ["web"], "name": "test@myprovider", "provider": "<PERSON><PERSON><PERSON><PERSON>", "rule": "Host(`foo.bar.other`)", "service": "foo-service@myprovider", "status": "enabled", "tls": {"passthrough": false}, "using": ["web"]}]