syntax = "proto3";

option java_multiple_files = true;
option java_package = "io.grpc.examples.helloworld";
option java_outer_classname = "HelloWorldProto";

package helloworld;

// The greeting service definition.
service Greeter {
  // Sends a greeting
  rpc Say<PERSON>ello (HelloRequest) returns (HelloReply) {};

  rpc StreamExample (StreamExampleRequest) returns (stream StreamExampleReply) {};

}

// The request message containing the user's name.
message HelloRequest {
  string name = 1;
}

// The response message containing the greetings
message HelloReply {
  string message = 1;
}

message StreamExampleRequest {}

message StreamExampleReply {
    string data = 1;
}