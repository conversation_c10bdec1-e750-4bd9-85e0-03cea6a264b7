services:
  consul:
    image: consul:1.6.2
    command:
      - agent
      - -server
      - -bootstrap
      - -ui
      - -client
      - 0.0.0.0
      - -hcl
      - 'connect { enabled = true }'

  consul-agent:
    image: consul:1.6.2
    command:
      - agent
      - -retry-join
      - consul
      - -client
      - 0.0.0.0

  whoami1:
    image: traefik/whoami
    hostname: whoami1

  whoami2:
    image: traefik/whoami
    hostname: whoami2

  whoami3:
    image: traefik/whoami
    hostname: whoami3

  whoamitcp:
    image: traefik/whoamitcp
    hostname: whoamitcp

  connect:
    image: hashicorpnomad/uuid-api:v5
    environment:
      PORT: 443
      BIND: 0.0.0.0
      CONSUL_HTTP_ADDR: http://consul:8500
