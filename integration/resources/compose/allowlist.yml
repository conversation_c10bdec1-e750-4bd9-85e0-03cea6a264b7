services:
  noOverrideAllowlist:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt1.rule: Host(`no.override.allowlist.docker.local`)
      traefik.http.routers.rt1.middlewares: wl1
      traefik.http.middlewares.wl1.ipallowlist.sourceRange: *******

  overrideIPStrategyRemoteAddrAllowlist:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt2.rule: Host(`override.remoteaddr.allowlist.docker.local`)
      traefik.http.routers.rt2.middlewares: wl2
      traefik.http.middlewares.wl2.ipallowlist.sourceRange: *******
      traefik.http.middlewares.wl2.ipallowlist.ipStrategy: true

  overrideIPStrategyDepthAllowlist:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt3.rule: Host(`override.depth.allowlist.docker.local`)
      traefik.http.routers.rt3.middlewares: wl3
      traefik.http.middlewares.wl3.ipallowlist.sourceRange: *******
      traefik.http.middlewares.wl3.ipallowlist.ipStrategy.depth: 3

  overrideIPStrategyExcludedIPsAllowlist:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt4.rule: Host(`override.excludedips.allowlist.docker.local`)
      traefik.http.routers.rt4.middlewares: wl4
      traefik.http.middlewares.wl4.ipallowlist.sourceRange: *******
      traefik.http.middlewares.wl4.ipallowlist.ipStrategy.excludedIPs: ********,********
