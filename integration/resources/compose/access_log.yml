services:
  server0:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-server0.entryPoints: web
      traefik.http.routers.rt-server0.rule: Path(`/test`)
      traefik.http.services.service1.loadbalancer.server.port: 80

  server1:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-server1.entryPoints: web
      traefik.http.routers.rt-server1.rule: Host(`frontend1.docker.local`)
      traefik.http.routers.rt-server1.service: service1
      traefik.http.services.service1.loadbalancer.server.port: 80

  server2:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-server2.entryPoints: web
      traefik.http.routers.rt-server2.rule: Host(`frontend2.docker.local`)
      traefik.http.services.service2.loadbalancer.server.port: 80

  server3:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-server3.entryPoints: web
      traefik.http.routers.rt-server3.rule: Host(`frontend2.docker.local`)
      traefik.http.services.service2.loadbalancer.server.port: 80

  authFrontend:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-authFrontend.entryPoints: httpFrontendAuth
      traefik.http.routers.rt-authFrontend.rule: Host(`frontend.auth.docker.local`)
      traefik.http.routers.rt-authFrontend.middlewares: basicauth
      traefik.http.middlewares.basicauth.basicauth.users: "test:$apr1$H6uskkkW$IgXLP6ewTrSuBkTrqE8wj/"
      traefik.http.services.service3.loadbalancer.server.port: 80

  digestAuthMiddleware:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-digestAuthMiddleware.entryPoints: digestAuth
      traefik.http.routers.rt-digestAuthMiddleware.rule: Host(`entrypoint.digest.auth.docker.local`)
      traefik.http.routers.rt-digestAuthMiddleware.middlewares: digestauth
      traefik.http.middlewares.digestauth.digestauth.users: test:traefik:a2688e031edb4be6a3797f3882655c05, test2:traefik:518845800f9e2bfb1f1f740ec24f074e
      traefik.http.services.service3.loadbalancer.server.port: 80

  frontendRedirect:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-frontendRedirect.entryPoints: frontendRedirect
      traefik.http.routers.rt-frontendRedirect.rule: Path(`/test`)
      traefik.http.routers.rt-frontendRedirect.middlewares: redirecthttp
      traefik.http.middlewares.redirecthttp.redirectScheme.scheme: http
      traefik.http.middlewares.redirecthttp.redirectScheme.port: 8000
      traefik.http.services.service3.loadbalancer.server.port: 80

  rateLimit:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-rateLimit.entryPoints: httpRateLimit
      traefik.http.routers.rt-rateLimit.rule: Host(`ratelimit.docker.local`)
      traefik.http.routers.rt-rateLimit.middlewares: rate
      traefik.http.middlewares.rate.ratelimit.average: 1
      traefik.http.middlewares.rate.ratelimit.burst: 2
      traefik.http.services.service3.loadbalancer.server.port: 80

  frontendAllowlist:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-frontendAllowlist.entryPoints: web
      traefik.http.routers.rt-frontendAllowlist.rule: Host(`frontend.allowlist.docker.local`)
      traefik.http.routers.rt-frontendAllowlist.middlewares: wl
      traefik.http.middlewares.wl.ipallowlist.sourcerange: *******/32
      traefik.http.services.service3.loadbalancer.server.port: 80

  preflightCORS:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.rt-preflightCORS.entryPoints: preflight
      traefik.http.routers.rt-preflightCORS.rule: Host(`preflight.docker.local`)
      traefik.http.routers.rt-preflightCORS.middlewares: preflightCORS
      traefik.http.middlewares.preflightCORS.headers.accessControlAllowMethods: OPTIONS, GET
      traefik.http.services.preflightCORS.loadbalancer.server.port: 80

  ping:
    image: traefik/whoami
    labels:
      traefik.enable: true
      traefik.http.routers.ping.entryPoints: ping
      traefik.http.routers.ping.rule: PathPrefix(`/ping`)
      traefik.http.routers.ping.service: ping@internal

      traefik.http.routers.ping-error.entryPoints: ping
      traefik.http.routers.ping-error.rule: PathPrefix(`/ping-error`)
      traefik.http.routers.ping-error.middlewares: errors, basicauth
      traefik.http.routers.ping-error.service: ping@internal
      traefik.http.middlewares.basicauth.basicauth.users: "test:$apr1$H6uskkkW$IgXLP6ewTrSuBkTrqE8wj/"
      traefik.http.middlewares.errors.errors.status: 401
      traefik.http.middlewares.errors.errors.service: service3
      traefik.http.middlewares.errors.errors.query: /
      traefik.http.services.service3.loadbalancer.server.port: 80
