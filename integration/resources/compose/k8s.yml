services:
  server:
    image: rancher/k3s:v1.21.14-k3s1
    privileged: true
    command:
      - server
      - --disable-agent
      - --disable=coredns
      - --disable=servicelb
      - --disable=traefik
      - --disable=local-storage
      - --disable=metrics-server
      - --log=/output/k3s.log
      - --bind-address=server
      - --tls-san=server
      - --tls-san=***********
      - --tls-san=***********
    environment:
      K3S_CLUSTER_SECRET: somethingtotallyrandom
      K3S_TOKEN: somethingtotallyrandom
      K3S_KUBECONFIG_OUTPUT: /output/kubeconfig.yaml
      K3S_KUBECONFIG_MODE: 666
    volumes:
      - ./fixtures/k8s/config.skip:/output
      - ./fixtures/k8s:/var/lib/rancher/k3s/server/manifests

  node:
    image: rancher/k3s:v1.21.14-k3s1
    privileged: true
    environment:
      K3S_TOKEN: somethingtotallyrandom
      K3S_URL: https://server:6443
      K3S_CLUSTER_SECRET: somethingtotallyrandom
