services:
  simple:
    image: swarm:1.0.0
    command: [ "manage", "token://blablabla" ]

  withtcplabels:
    image: traefik/whoamitcp
    command: [ "-name", "my.super.host" ]
    labels:
      traefik.tcp.Routers.Super.Rule: HostSNI(`my.super.host`)
      traefik.tcp.Routers.Super.tls: true
      traefik.tcp.Services.Super.Loadbalancer.server.port: 8080

  withlabels1:
    image: swarm:1.0.0
    command: [ "manage", "token://blabla" ]
    labels:
      traefik.http.Routers.Super.Rule: Host(`my.super.host`)

  withlabels2:
    image: swarm:1.0.0
    command: [ "manage", "token://blablabla" ]
    labels:
      traefik.http.Routers.SuperHost.Rule: Host(`my-super.host`)

  withonelabelmissing:
    image: swarm:1.0.0
    command: [ "manage", "token://blabla" ]
    labels:
      traefik.random.value: my.super.host

  powpow:
    image: swarm:1.0.0
    command: [ "manage", "token://blabla" ]
    labels:
      traefik.http.Routers.Super.Rule: Host(`my.super.host`)
      traefik.http.Services.powpow.LoadBalancer.server.Port: 2375

  wrr-server:
    image: traefik/whoami
    labels:
      traefik.http.Routers.wrr-server.Rule: Host(`my.wrr.host`)
      traefik.http.Services.wrr-server.LoadBalancer.server.Weight: 4
  wrr-server2:
    image: traefik/whoami
    labels:
      traefik.http.Routers.wrr-server.Rule: Host(`my.wrr.host`)
      traefik.http.Services.wrr-server.LoadBalancer.server.Weight: 1
