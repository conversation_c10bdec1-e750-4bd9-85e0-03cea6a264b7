[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[api]
  insecure = true

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]

  [entryPoints.web]
    address = ":8000"

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router]
    service = "mirror"
    rule = "Path(`/whoami`)"

  [http.routers.router2]
    service = "mirrorWithMaxBody"
    rule = "Path(`/whoamiWithMaxBody`)"

  [http.routers.router3]
    service = "mirrorWithoutBody"
    rule = "Path(`/whoamiWithoutBody`)"


[http.services]
  [http.services.mirror.mirroring]
    service = "service1"
  [[http.services.mirror.mirroring.mirrors]]
    name = "mirror1"
    percent = 10
  [[http.services.mirror.mirroring.mirrors]]
    name = "mirror2"
    percent = 50

  [http.services.mirrorWithMaxBody.mirroring]
    service = "service1"
    maxBodySize = 8
  [[http.services.mirrorWithMaxBody.mirroring.mirrors]]
    name = "mirror1"
    percent = 10
  [[http.services.mirrorWithMaxBody.mirroring.mirrors]]
    name = "mirror2"
    percent = 50

  [http.services.mirrorWithoutBody.mirroring]
    service = "service1"
    mirrorBody = false
  [[http.services.mirrorWithoutBody.mirroring.mirrors]]
    name = "mirror1"
    percent = 10
  [[http.services.mirrorWithoutBody.mirroring.mirrors]]
    name = "mirror2"
    percent = 50


  [http.services.service1.loadBalancer]
    [[http.services.service1.loadBalancer.servers]]
      url = "{{ .MainServer }}"
  [http.services.mirror1.loadBalancer]
    [[http.services.mirror1.loadBalancer.servers]]
      url = "{{ .Mirror1Server }}"
  [http.services.mirror2.loadBalancer]
      [[http.services.mirror2.loadBalancer.servers]]
        url = "{{ .Mirror2Server }}"

