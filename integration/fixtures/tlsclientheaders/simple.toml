[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"

[serversTransport]
  rootCAs = [ """{{ .RootCertContent }}""" ]

[entryPoints]
  [entryPoints.websecure]
    address = ":8443"

[api]
  insecure = true

[providers]
  [providers.docker]
    watch = true

  [providers.file]
    filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[tls.options]
  [tls.options.default.clientAuth]
    caFiles = [ """{{ .RootCertContent }}""" ]
    clientAuthType = "RequireAndVerifyClientCert"

[tls.stores]
  [tls.stores.default.defaultCertificate]
    certFile = """{{ .ServerCertContent }}"""
    keyFile  = """{{ .ServerKeyContent }}"""
