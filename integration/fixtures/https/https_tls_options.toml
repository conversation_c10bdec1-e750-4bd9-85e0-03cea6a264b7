[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.websecure]
    address = ":4443"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    service = "service1"
    rule = "Host(`snitest.com`)"
    [http.routers.router1.tls]
      options = "foo"

  [http.routers.router2]
    service = "service2"
    rule = "Host(`snitest.org`)"
    [http.routers.router2.tls]
      options = "bar"

  [http.routers.router3]
    service = "service2"
    rule = "Host(`snitest.org`)"
    [http.routers.router3.tls]
      options = "unknown"

  [http.routers.router4]
    service = "service1"
    rule = "Host(`snitest.net`)"
    [http.routers.router4.tls]
      options = "foo"

  [http.routers.router5]
    service = "service1"
    rule = "Host(`snitest.net`)"
    [http.routers.router5.tls]
      options = "baz"

[http.services]
  [http.services.service1]
    [http.services.service1.loadBalancer]
      [[http.services.service1.loadBalancer.servers]]
        url = "http://127.0.0.1:9010"

  [http.services.service2]
    [http.services.service2.loadBalancer]
      [[http.services.service2.loadBalancer.servers]]
        url = "http://127.0.0.1:9020"

[[tls.certificates]]
  certFile = "fixtures/https/snitest.com.cert"
  keyFile = "fixtures/https/snitest.com.key"

[[tls.certificates]]
  certFile = "fixtures/https/snitest.org.cert"
  keyFile = "fixtures/https/snitest.org.key"

[tls.options]

  [tls.options.foo]
    minVersion = "VersionTLS11"

  [tls.options.baz]
    minVersion = "VersionTLS11"

  [tls.options.bar]
    minVersion = "VersionTLS12"

  [tls.options.default]
    minVersion = "VersionTLS12"
