[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8888"

  [entryPoints.websecure]
    address = ":8443"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    entryPoints = [ "web" ]
    rule = "Host(`example.com`)"
    middlewares = ["redirect-https"]
    service = "service1"

  [http.routers.router1TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`example.com`)"
    service = "service1"
    [http.routers.router1TLS.tls]

  [http.routers.router2]
    entryPoints = [ "web" ]
    rule = "Host(`example2.com`)"
    middlewares = ["redirect-https", "api-slash-strip"]
    service = "service1"

  [http.routers.router2TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`example2.com`)"
    service = "service1"
    [http.routers.router2TLS.tls]

  [http.routers.router3]
    entryPoints = [ "web" ]
    rule = "Host(`test.com`)"
    middlewares = ["redirect-https", "foo-add-prefix"]
    service = "service1"

  [http.routers.router3TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`test.com`)"
    service = "service1"
    [http.routers.router3TLS.tls]

  [http.routers.router4]
    entryPoints = [ "web" ]
    rule = "Host(`test2.com`)"
    middlewares = ["redirect-https", "foo-slash-add-prefix"]
    service = "service1"

  [http.routers.router4TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`test2.com`)"
    service = "service1"
    [http.routers.router4TLS.tls]

  [http.routers.router5]
    entryPoints = [ "web" ]
    rule = "Host(`foo.com`)"
    middlewares = ["redirect-https", "id-strip-regex-prefix"]
    service = "service1"

  [http.routers.router5TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`foo.com`)"
    service = "service1"
    [http.routers.router5TLS.tls]

  [http.routers.router6]
    entryPoints = [ "web" ]
    rule = "Host(`foo2.com`)"
    middlewares = ["redirect-https", "id-slash-strip-regex-prefix"]
    service = "service1"

  [http.routers.router6TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`foo2.com`)"
    service = "service1"
    [http.routers.router6TLS.tls]

  [http.routers.router7]
    entryPoints = [ "web" ]
    rule = "Host(`bar.com`)"
    middlewares = ["redirect-https", "api-regex-replace"]
    service = "service1"

  [http.routers.router7TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`bar.com`)"
    service = "service1"
    [http.routers.router7TLS.tls]

  [http.routers.router8]
    entryPoints = [ "web" ]
    rule = "Host(`bar2.com`)"
    middlewares = ["redirect-https", "api-slash-regex-replace"]
    service = "service1"

  [http.routers.router8TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`bar2.com`)"
    service = "service1"
    [http.routers.router8TLS.tls]

  [http.routers.router9]
    entryPoints = [ "web" ]
    rule = "Host(`pow.com`)"
    middlewares = ["redirect-https", "api-replace-path"]
    service = "service1"

  [http.routers.router9TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`pow.com`)"
    service = "service1"
    [http.routers.router9TLS.tls]

  [http.routers.router10]
    entryPoints = [ "web" ]
    rule = "Host(`pow2.com`)"
    middlewares = ["redirect-https", "api-slash-replace-path"]
    service = "service1"

  [http.routers.router10TLS]
    entryPoints = [ "websecure" ]
    rule = "Host(`pow2.com`)"
    service = "service1"
    [http.routers.router10TLS.tls]

[http.middlewares]
  [http.middlewares.api-strip.stripPrefix]
    prefixes = ["/api"]
  [http.middlewares.api-slash-strip.stripPrefix]
    prefixes = ["/api/"]
  [http.middlewares.foo-add-prefix.addPrefix]
    prefix = "/foo"
  [http.middlewares.foo-slash-add-prefix.addPrefix]
    prefix = "/foo/"
  [http.middlewares.id-strip-regex-prefix.stripPrefixRegex]
    regex = ["/[a-z]+"]
  [http.middlewares.id-slash-strip-regex-prefix.stripPrefixRegex]
    regex = ["/[a-z]+/"]
  [http.middlewares.api-regex-replace.replacePathRegex]
    regex = "/api"
    replacement = "/"
  [http.middlewares.api-slash-regex-replace.replacePathRegex]
    regex = "/api/"
    replacement = "/"
  [http.middlewares.api-replace-path.replacePath]
    path = "/api"
  [http.middlewares.api-slash-replace-path.replacePath]
    path = "/api/"
  [http.middlewares.redirect-https.redirectScheme]
    scheme = "https"
    port = "8443"

[http.services]
  [http.services.service1]
    [http.services.service1.loadBalancer]
      [[http.services.service1.loadBalancer.servers]]
        url = "http://127.0.0.1:80"
