[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.websecure]
    address = ":4443"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router3]
    entrypoints=["unknown-entrypoint"]
    service = "service1"
    rule = "Host(`mydomain.com`)"

  [http.routers.router4]
    service = "service1"
    rule = "Host(`snitest.net`)"
    [http.routers.router4.tls]
      options = "foo"

  [http.routers.router5]
    service = "service1"
    rule = "Host(`snitest.net`)"
    middlewares = [ "unknown" ]
    [http.routers.router5.tls]
      options = "baz"

[http.services]
  [http.services.service1]
    [http.services.service1.loadBalancer]
      [[http.services.service1.loadBalancer.servers]]
        url = "http://127.0.0.1:9010"

[tls.options]

  [tls.options.foo]
    minversion = "VersionTLS11"

  [tls.options.baz]
    minversion = "VersionTLS11"

[tcp.routers]
  [tcp.routers.router3]
    entrypoints=["unknown-entrypoint"]
    service = "service1"
    rule = "HostSNI(`mydomain.com`)"
  [tcp.routers.router4]
    entrypoints=["websecure"]
    service = "service1"
    rule = "Host(`mydomain.com`)"

[tcp.services]
  [tcp.services.service1]
    [tcp.services.service1.loadBalancer]
      [[tcp.services.service1.loadBalancer.servers]]
        address = "127.0.0.1:9010"

[udp.routers]
  [udp.routers.router3]
    entrypoints=["unknown-entrypoint"]
    service = "service1"

[udp.services]
  [udp.services.service1]
    [udp.services.service1.loadBalancer]
      [[udp.services.service1.loadBalancer.servers]]
        address = "127.0.0.1:9010"
