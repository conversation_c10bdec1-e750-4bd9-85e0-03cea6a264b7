[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    rule = "Host(`test.localhost`)"
    service = "service2"

  [http.routers.router2]
    rule = "Path(`/test`)"
    middlewares = ["circuitbreaker"]
    service = "service1"

[http.middlewares]
  [http.middlewares.circuitbreaker.circuitBreaker]
    expression = "NetworkErrorRatio() > 0.5"

[http.services]
  [http.services.service1.loadBalancer]
    [[http.services.service1.loadBalancer.servers]]
      url = "http://**********:80"
    [[http.services.service1.loadBalancer.servers]]
      url = "http://**********:80"
    
  [http.services.service2]
    [http.services.service2.loadBalancer]
      [[http.services.service2.loadBalancer.servers]]
        url = "http://**********:80"
      [[http.services.service2.loadBalancer.servers]]
        url = "http://**********:80"
