[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[serversTransport]
  insecureSkipVerify=true

[entryPoints]
  [entryPoints.wss]
    address = ":8000"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    service = "service1"
    rule = "Path(`/echo`) || Path(`/ws`)"
    [http.routers.router1.tls]

[http.services]
  [http.services.service1]
    [http.services.service1.loadBalancer]
      passHostHeader = true
      [[http.services.service1.loadBalancer.servers]]
        url = "{{ .WebsocketServer }}"

[tls.stores]
  [tls.stores.default.defaultCertificate]
    certFile = "resources/tls/local.cert"
    keyFile = "resources/tls/local.key"
