[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    service = "service1"
    rule = "PathPrefix(`/foo`) || PathPrefix(`/bar`)"

  [http.routers.router2]
    service = "service1"
    rule = "PathPrefix(`/foo`, `/bar`)"

  [http.routers.router3]
    service = "service1"
    rule = "QueryRegexp(`foo`, `bar`)"
    ruleSyntax = "v2"

[http.services]
  [http.services.service1]
    [http.services.service1.loadBalancer]
      [http.services.service1.loadBalancer.servers]
