[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.tcp]
    address = ":8093"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[tcp]
  [tcp.routers]
    [tcp.routers.to-whoami-no-cert]
      rule = "HostSNI(`whoami-c.test`)"
      service = "whoami-no-cert"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-no-cert.tls]
        options = "foo"

    [tcp.routers.to-whoami-sni-strict]
      rule = "HostSNI(`whoami-d.test`)"
      service = "whoami-no-cert"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-sni-strict.tls]
        options = "bar"

    [tcp.routers.to-whoami-invalid-tls]
      rule = "HostSNI(`whoami-i.test`)"
      service = "whoami-no-cert"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-invalid-tls.tls]
        options = "invalid"

    [tcp.services.whoami-no-cert]
      [tcp.services.whoami-no-cert.loadBalancer]
        [[tcp.services.whoami-no-cert.loadBalancer.servers]]
        address = "{{ .WhoamiNoCert }}"

[tls.options]

  [tls.options.foo]
    minVersion = "VersionTLS12"

  [tls.options.bar]
    minVersion = "VersionTLS13"

  [tls.options.invalid.clientAuth]
    # Missing CA files to have an invalid mTLS configuration.
    clientAuthType = "RequireAndVerifyClientCert"
