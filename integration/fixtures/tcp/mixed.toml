[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.tcp]
    address = ":8093"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http]
  [http.routers]
    [http.routers.my-router]
     rule = "Path(`/test`)"
     service = "whoami"
     entrypoints = ["tcp"]
     
    [http.routers.my-https-router]
      entryPoints=["tcp"]
      rule = "Path(`/whoami/`)"
      service = "whoami"
      [http.routers.my-https-router.tls]
    [http.routers.api]
      rule = "PathPrefix(`/api`)"
      service = "api@internal"
      entryPoints = ["traefik"]

  [http.services]
    [http.services.whoami.loadBalancer]
      [[http.services.whoami.loadBalancer.servers]]
        url = "{{ .Whoami }}"
[tcp]
  [tcp.routers]
    [tcp.routers.to-whoami-a]
      rule = "HostSNI(`whoami-a.test`)"
      service = "whoami-a"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-a.tls]
        passthrough = true

    [tcp.routers.to-whoami-b]
      rule = "HostSNI(`whoami-b.test`)"
      service = "whoami-b"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-b.tls]
        passthrough = true

    [tcp.routers.to-whoami-no-cert]
      rule = "HostSNI(`whoami-c.test`)"
      service = "whoami-no-cert"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-no-cert.tls]

    [tcp.services.whoami-a.loadBalancer]
      [[tcp.services.whoami-a.loadBalancer.servers]]
        address = "{{ .WhoamiA }}"

    [tcp.services.whoami-b.loadBalancer]
      [[tcp.services.whoami-b.loadBalancer.servers]]
        address = "{{ .WhoamiB }}"

    [tcp.services.whoami-no-cert.loadBalancer]
      [[tcp.services.whoami-no-cert.loadBalancer.servers]]
        address = "{{ .WhoamiNoCert }}"

[[tls.certificates]]
  certFile = "fixtures/tcp/whoami-c.crt"
  keyFile = "fixtures/tcp/whoami-c.key"
