[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.websecure]
    address = ":4443"
  [entryPoints.udp]
    address = ":4443/udp"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[tcp.routers]
  [tcp.routers.router1]
    service = "service1"
    rule = "HostSNI(`snitest.net`)"
    [tcp.routers.router1.tls]

  [tcp.routers.router2]
    service = "service2"
    rule = "HostSNI(`snitest.com`)"
    [tcp.routers.router2.tls]

[tcp.services]
  [tcp.services.service1]

  [tcp.services.service2]
    [tcp.services.service2.loadBalancer]
      [[tcp.services.service2.loadBalancer.servers]]
        address = "127.0.0.1:9010"
