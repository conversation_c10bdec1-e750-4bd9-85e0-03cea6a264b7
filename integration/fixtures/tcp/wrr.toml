[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.tcp]
    address = ":8093"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##
[tcp]
  [tcp.routers]
    [tcp.routers.to-whoami-b]
      rule = "HostSNI(`whoami-b.test`)"
      service = "whoami"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-b.tls]
        passthrough=true

    [[tcp.services.whoami.weighted.services]]
        name="whoami-b"
        weight=3
    [[tcp.services.whoami.weighted.services]]
        name="whoami-ab"
        weight=1

    [tcp.services.whoami-b.loadBalancer]
      [[tcp.services.whoami-b.loadBalancer.servers]]
        address = "{{ .WhoamiB }}"

    [tcp.services.whoami-ab.loadBalancer]
      [[tcp.services.whoami-ab.loadBalancer.servers]]
        address = "{{ .WhoamiAB }}"
