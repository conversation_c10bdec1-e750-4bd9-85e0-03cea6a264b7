[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.tcp]
    address = ":8093"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[tcp]
  [tcp.routers]
    [tcp.routers.to-whoami-a]
      rule = "HostSNI(`whoami-a.test`)"
      service = "whoami-a"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-a.tls]
        passthrough = true

    [tcp.routers.to-whoami-b]
      rule = "HostSNI(`whoami-b.test`)"
      service = "whoami-b"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-b.tls]
        passthrough = true

    [tcp.routers.to-whoami-no-cert]
      rule = "HostSNI(`whoami-c.test`)"
      service = "whoami-no-cert"
      entryPoints = [ "tcp" ]
      [tcp.routers.to-whoami-no-cert.tls]

    [tcp.routers.to-whoami-no-tls]
      entryPoints = ["tcp"]
      rule="HostSNI(`*`)"
      service = "whoami-no-tls"

  [tcp.services]
    [tcp.services.whoami-no-tls.loadBalancer]
      [[tcp.services.whoami-no-tls.loadBalancer.servers]]
        address = "{{ .WhoamiNoTLS }}"

    [tcp.services.whoami-a.loadBalancer]
      [[tcp.services.whoami-a.loadBalancer.servers]]
        address = "{{ .WhoamiA }}"

    [tcp.services.whoami-b.loadBalancer]
      [[tcp.services.whoami-b.loadBalancer.servers]]
        address = "{{ .WhoamiB }}"

    [tcp.services.whoami-no-cert.loadBalancer]
      [[tcp.services.whoami-no-cert.loadBalancer.servers]]
        address = "{{ .WhoamiNoCert }}"

