[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.tcp]
    address = ":8093"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[tcp]
  [tcp.routers]
    [tcp.routers.to-whoami-no-tls]
      entryPoints = ["tcp"]
      rule = "HostSNI(`*`)"
      service = "whoami-no-tls"

  [tcp.services]
    [tcp.services.whoami-no-tls.loadBalancer]
      [[tcp.services.whoami-no-tls.loadBalancer.servers]]
        address = "{{ .WhoamiNoTLSAddress }}"

[http]
  [http.routers]
    [http.routers.to-whoami]
      entryPoints = ["tcp"]
      rule="PathPrefix(`/`)"
      service = "whoami"
      [http.routers.to-whoami.tls]

  [http.services]
    [http.services.whoami.loadBalancer]
      [[http.services.whoami.loadBalancer.servers]]
        url = "{{ .WhoamiURL }}"
