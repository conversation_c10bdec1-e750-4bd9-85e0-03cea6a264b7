[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true


[entryPoints]
  [entryPoints.tcp]
    address = ":8093"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[tcp]
  [tcp.routers]
    [tcp.routers.to-whoami-a]
      entryPoints = ["tcp"]
      rule = "HostSNI(`whoami-a.test`)"
      service = "whoami-a"
      middlewares = ["blocking-ipallowlist"]
    [tcp.routers.to-whoami-a.tls]
      passthrough = true

    [tcp.routers.to-whoami-b]
      entryPoints = ["tcp"]
      rule = "HostSNI(`whoami-b.test`)"
      service = "whoami-b"
      middlewares = ["allowing-ipallowlist"]
    [tcp.routers.to-whoami-b.tls]
      passthrough = true

  [tcp.services]
    [tcp.services.whoami-a.loadBalancer]
      [[tcp.services.whoami-a.loadBalancer.servers]]
        address = "{{ .WhoamiA }}"

    [tcp.services.whoami-b.loadBalancer]
      [[tcp.services.whoami-b.loadBalancer.servers]]
        address = "{{ .WhoamiB }}"

[tcp.middlewares]
  [tcp.middlewares.allowing-ipallowlist.ipAllowList]
    sourceRange = ["127.0.0.1/32"]
  [tcp.middlewares.blocking-ipallowlist.ipAllowList]
    sourceRange = ["***************/32"]
