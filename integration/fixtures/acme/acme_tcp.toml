[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = "{{ .PortHTTP }}"
  [entryPoints.websecure]
    address = "{{ .PortHTTPS }}"

{{range $name, $resolvers := .Acme }}

[certificatesResolvers.{{ $name }}.acme]
  email = "<EMAIL>"
  storage = "/tmp/acme.json"
  keyType = "{{ $resolvers.ACME.KeyType }}"
  caServer = "{{ $resolvers.ACME.CAServer }}"

  {{if $resolvers.ACME.HTTPChallenge }}
  [certificatesResolvers.{{ $name }}.acme.httpChallenge]
    entryPoint = "{{ $resolvers.ACME.HTTPChallenge.EntryPoint }}"
  {{end}}

  {{if $resolvers.ACME.TLSChallenge }}
  [certificatesResolvers.{{ $name }}.acme.tlsChallenge]
  {{end}}

{{end}}

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[tcp.services]
  [tcp.services.test.loadBalancer]
    [[tcp.services.test.loadBalancer.servers]]
      address = "127.0.0.1:9010"

[tcp.routers]
  [tcp.routers.test]
    entryPoints = ["websecure"]
    rule = "HostSNI(`traefik.acme.wtf`)"
    service = "test"
    [tcp.routers.test.tls]
      certResolver  = "default"
