[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"

[entryPoints]
  [entryPoints.web]
    address = "{{ .PortHTTP }}"
  [entryPoints.websecure]
    address = "{{ .PortHTTPS }}"

{{range $name, $resolvers := .Acme }}

[certificatesResolvers.{{ $name }}.acme]
  email = "<EMAIL>"
  storage = "/tmp/acme.json"
  keyType = "{{ $resolvers.ACME.KeyType }}"
  caServer = "{{ $resolvers.ACME.CAServer }}"

  {{if $resolvers.ACME.HTTPChallenge }}
  [certificatesResolvers.{{ $name }}.acme.httpChallenge]
    entryPoint = "{{ $resolvers.ACME.HTTPChallenge.EntryPoint }}"
  {{end}}

  {{if $resolvers.ACME.TLSChallenge }}
  [certificatesResolvers.{{ $name }}.acme.tlsChallenge]
  {{end}}

{{end}}

[api]
  insecure = true

[providers]
  [providers.file]
    filename = "fixtures/acme/certificates.toml"
    watch = true
