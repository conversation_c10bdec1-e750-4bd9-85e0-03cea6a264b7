[http.services]
  [http.services.test.loadBalancer]
    [[http.services.test.loadBalancer.servers]]
      url = "http://127.0.0.1:9010"

[http.routers]
  [http.routers.test]
    entryPoints = ["websecure"]
    rule = "Host(`traefik.acme.wtf`)"
    service = "test"
    [http.routers.test.tls]

[[tls.certificates]]
  stores = ["default"]
  certFile = "fixtures/acme/ssl/wildcard.crt"
  keyFile = "fixtures/acme/ssl/wildcard.key"
