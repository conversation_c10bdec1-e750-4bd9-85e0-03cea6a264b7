---
kind: GatewayClass
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: traefik
spec:
  controllerName: traefik.io/gateway-controller

---
kind: Namespace
apiVersion: v1
metadata:
  name: traefik

---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: traefik
  namespace: traefik

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: traefik
  namespace: traefik
  labels:
    app: traefik

spec:
  replicas: 1
  selector:
    matchLabels:
      app: traefik
  template:
    metadata:
      labels:
        app: traefik
    spec:
      serviceAccountName: traefik
      containers:
        - name: traefik
          image: traefik/traefik:latest
          imagePullPolicy: Never
          args:
            - --log.level=DEBUG
            - --api.insecure
            - --entrypoints.web.address=:80
            - --entrypoints.websecure.address=:443
            - --entrypoints.web8080.address=:8080
            - --entrypoints.traefik.address=:9000
            - --providers.kubernetesgateway.experimentalChannel
            - --providers.kubernetesgateway.statusaddress.service.namespace=traefik
            - --providers.kubernetesgateway.statusaddress.service.name=traefik
          ports:
            - name: web
              containerPort: 80
            - name: websecure
              containerPort: 443
            - name: web8080
              containerPort: 8080
            - name: traefik
              containerPort: 9000

---
apiVersion: v1
kind: Service
metadata:
  name: traefik
  namespace: traefik
spec:
  type: LoadBalancer
  selector:
    app: traefik
  ports:
    - port: 80
      name: web
      targetPort: web
    - port: 443
      name: websecure
      targetPort: websecure
    - port: 8080
      name: web8080
      targetPort: web8080
    - port: 9000
      name: traefik
      targetPort: traefik
