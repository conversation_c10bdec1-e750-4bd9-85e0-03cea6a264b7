[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

[api]
  insecure = true

[providers]
  [providers.consulCatalog]
    exposedByDefault = true
    refreshInterval = "500ms"
    defaultRule = "{{ .DefaultRule }}"
    watch = true
  [providers.consulCatalog.endpoint]
    address = "{{ .ConsulAddress }}"
