[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8080"

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    rule = "Host(`test.local`)"
    service = "service1"
    middlewares = ["error"]

[http.middlewares]
  [http.middlewares.error.errors]
    status = ["500-502", "503-599"]
    service = "error"
    query = "/50x.html"

[http.services]
  [http.services.service1.loadBalancer]
    passHostHeader = true
    [[http.services.service1.loadBalancer.servers]]
      url = "{{.Server1}}"

  [http.services.error.loadBalancer]
    [[http.services.error.loadBalancer.servers]]
      url = "http://{{.Server2}}:80"
