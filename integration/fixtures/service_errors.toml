[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.websecure]
    address = ":4443"
  [entryPoints.udp]
    address = ":4443/udp"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    service = "service1"
    rule = "Host(`snitest.net`)"

  [http.routers.router2]
    service = "service2"
    rule = "Host(`snitest.com`)"

[http.services]
  [http.services.service1]

  [http.services.service2]
    [http.services.service2.loadBalancer]
      [[http.services.service2.loadBalancer.servers]]
        url = "http://127.0.0.1:9010"
