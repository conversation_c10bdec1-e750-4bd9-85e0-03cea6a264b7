[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    service = "service1"
    rule = "PathPrefix(`/`)"

  [http.routers.router2]
    service = "service1"
    middlewares = ["autodetect"]
    rule = "PathPrefix(`/autodetect`)"

[http.services]
  [http.services.service1]
    [http.services.service1.loadBalancer]
      passHostHeader = true
      [[http.services.service1.loadBalancer.servers]]
        url = "{{ .Server }}"

[http.middlewares.autodetect.contentType]
