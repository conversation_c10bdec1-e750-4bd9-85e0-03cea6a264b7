[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[entryPoints]
  [entryPoints.web]
    address = ":8000"
  [entryPoints.web.http]
    maxHeaderBytes = 1310720

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.test-router]
    entryPoints = ["web"]
    service = "test-service"
    rule = "Host(`127.0.0.1`)"

[http.services]
  [http.services.test-service]
    [[http.services.test-service.loadBalancer.servers]]
      url = "{{ .TestServer }}"
