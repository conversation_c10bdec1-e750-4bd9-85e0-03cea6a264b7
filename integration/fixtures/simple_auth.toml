[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

  [entryPoints.traefik]
    address = ":8001"

[api]
  insecure = true
  middlewares = ["authentication@file"]

[ping]

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.middlewares]
  [http.middlewares.authentication.basicAuth]
    users = ["test:$apr1$H6uskkkW$IgXLP6ewTrSuBkTrqE8wj/", "test2:$apr1$d9hr9HBB$4HxwgUir3HP4EsggP/QNo0"]
