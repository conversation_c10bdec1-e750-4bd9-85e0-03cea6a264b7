[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    rule = "Host(`test.localhost`)"
    middlewares = ["cors"]
    service = "service1"

  [http.routers.router2]
    rule = "Host(`test2.localhost`)"
    middlewares = ["nocors"]
    service = "service1"

[http.middlewares]
  [http.middlewares.cors.headers]
    accessControlAllowMethods= ["GET", "OPTIONS", "PUT"]
    accessControlAllowOriginList = ["https://foo.bar.org"]
    accessControlMaxAge = 100
    addVaryHeader = true

  [http.middlewares.nocors.Headers]
    [http.middlewares.nocors.Headers.CustomResponseHeaders]
      X-Custom-Response-Header = "True"

[http.services]
  [http.services.service1.loadBalancer]
    [[http.services.service1.loadBalancer.servers]]
      url = "http://127.0.0.1:9000"
