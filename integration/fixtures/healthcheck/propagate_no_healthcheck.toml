[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.wsp-router-1]
    service = "wsp-service1"
    rule = "Host(`root.localhost`)"

  [http.routers.noop]
    service = "noop@internal"
    rule = "Host(`noop.localhost`)"

[http.services]
  [http.services.wsp-service1.weighted]
    [http.services.wsp-service1.weighted.healthcheck]
    [[http.services.wsp-service1.weighted.services]]
      name = "wsp1"
      weight = 1

  [http.services.wsp1.loadBalancer]
    [[http.services.wsp1.loadBalancer.servers]]
      url = "http://{{.Server1}}:80"
