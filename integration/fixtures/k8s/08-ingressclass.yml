apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: traefik-keep
spec:
  controller: traefik.io/ingress-controller

---
kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: "whoami-keep-route"
spec:
  ingressClassName: "traefik-keep"
  rules:
  - host: "whoami.test.keep"
    http:
      paths:
      - path: "/keep"
        backend:
          service:
            name: whoami
            port:
              number: 80
        pathType: Prefix

---
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: traefik-drop
spec:
  controller: traefik.io/ingress-controller

---
kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: "whoami-drop-route"
spec:
  ingressClassName: "traefik-drop"
  rules:
  - host: "whoami.test.drop"
    http:
      paths:
      - path: "/drop"
        backend:
          service:
            name: whoami
            port:
              number: 80
        pathType: Prefix

---
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: traefik-not-ingress-controller
spec:
  controller: not.tr43phic.io/ingress-controller

---
kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: "whoami-drop-ingress"
spec:
  ingressClassName: "traefik-not-ingress-controller"
  rules:
  - host: "whoami.test.not.ingress"
    http:
      paths:
      - path: "/ingress"
        backend:
          service:
            name: whoami
            port:
              number: 80
        pathType: Prefix
