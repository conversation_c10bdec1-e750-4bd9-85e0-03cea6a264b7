---
kind: GatewayClass
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: my-gateway-class
spec:
  controllerName: traefik.io/gateway-controller

---
kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: my-gateway
  namespace: default
spec:
  gatewayClassName: my-gateway-class
  listeners:
    - name: http
      protocol: HTTP
      port: 8180
      allowedRoutes:
        kinds:
          - kind: HTTPRoute

---
kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: my-tcp-gateway
  namespace: default
spec:
  gatewayClassName: my-gateway-class
  listeners:
    - name: tcp
      protocol: TCP
      port: 8193
      allowedRoutes:
        kinds:
          - kind: TCPRoute

---
apiVersion: v1
kind: Secret
metadata:
  name: supersecret
  namespace: default

data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNJakNDQVl1Z0F3SUJBZ0lRS1F6S1hWV0duODRNNzk2QmhGUzV0VEFOQmdrcWhraUc5dzBCQVFzRkFEQVMKTVJBd0RnWURWUVFLRXdkQlkyMWxJRU52TUNBWERUY3dNREV3TVRBd01EQXdNRm9ZRHpJd09EUXdNVEk1TVRZdwpNREF3V2pBU01SQXdEZ1lEVlFRS0V3ZEJZMjFsSUVOdk1JR2ZNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0R05BRENCCmlRS0JnUURsMHNndm5HSnJOMEt1NXVQanBVZjNwTkY2MTkyL0xSb1FjMmFCeENORkdtZW5XekhsZXpoS3Rnam4KZk0velRxeGU1Y3M5QzBvKzlpdFNrRTBzNEp0S0lob1R6NGEvbklCUmxRZ2hsWkRTQ2ZFVjdXdWxLZGVqbWE3Swp3MittUDVLYy9Qa0ozRkxPSCt0blJRSVZPakZmeDBhMllDS2VxTFJWRmhGOWlMSFBWd0lEQVFBQm8zY3dkVEFPCkJnTlZIUThCQWY4RUJBTUNBcVF3RXdZRFZSMGxCQXd3Q2dZSUt3WUJCUVVIQXdFd0R3WURWUjBUQVFIL0JBVXcKQXdFQi96QWRCZ05WSFE0RUZnUVVyZERBNGFIMHc2WjJHc2dxa3FHMHRqNlFZL2t3SGdZRFZSMFJCQmN3RllJVApkR3h6TG1admJ5NWxlR0Z0Y0d4bExtTnZiVEFOQmdrcWhraUc5dzBCQVFzRkFBT0JnUUIvVFBHcElMUGg0Nlp4CnVXZFM4WDFNWEc0ODVQSlNKYVhxZUNsTW9EVEQxdlVwa0Jzd1hEUUVESFRMQkU0SGROaEJaaUlpOFFLQjZCS1IKZEVqU0xFbmlhK0ExUkwyRjdIa05MbU1ycFVjT3lzdzBiOFg1LzkydkpGYStScXgxdjJwQ0FIUHRGUE9ZM240NQoza3lGZy96ZXUwd2w0NW80MUtNL0ZJT1ljWFA3dVE9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  tls.key: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

---
kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: my-tls-gateway
  namespace: default
spec:
  gatewayClassName: my-gateway-class
  listeners:
    - name: tls-9001
      protocol: TLS
      port: 9001
      tls:
        mode: Passthrough
      allowedRoutes:
        kinds:
          - kind: TLSRoute
        namespaces:
          from: Same

    - name: tls-9002
      protocol: TLS
      port: 9002
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: supersecret
      allowedRoutes:
        kinds:
          - kind: TCPRoute

---
kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: my-https-gateway
  namespace: default
spec:
  gatewayClassName: my-gateway-class
  listeners:
    - name: https
      protocol: HTTPS
      port: 8443
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: supersecret
      allowedRoutes:
        kinds:
          - kind: HTTPRoute

---
kind: HTTPRoute
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: http-app-1
  namespace: default
spec:
  parentRefs:
    - name: my-gateway
    - name: my-https-gateway
  hostnames:
    - "foo.com"
  rules:
    - matches:
        - path:
            type: Exact
            value: /bar
      backendRefs:
        - name: whoami
          port: 80
          weight: 1

---
kind: TCPRoute
apiVersion: gateway.networking.k8s.io/v1alpha2
metadata:
  name: tcp-app-1
  namespace: default
spec:
  parentRefs:
    - name: my-tcp-gateway
    - name: my-tls-gateway
  rules:
    - backendRefs:
        - name: whoamitcp
          port: 8080
          weight: 1

---
kind: TLSRoute
apiVersion: gateway.networking.k8s.io/v1alpha2
metadata:
  name: tls-app-1
  namespace: default
spec:
  parentRefs:
    - name: my-tls-gateway
  hostnames:
    - foo.bar
  rules:
    - backendRefs:
        - name: whoamitcp
          port: 8080
          weight: 1
