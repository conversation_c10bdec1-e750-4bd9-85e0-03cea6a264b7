---
apiVersion: v1
kind: Namespace
metadata:
  name: other-ns

---
apiVersion: v1
kind: Service
metadata:
  name: whoami
  namespace: other-ns

spec:
  ports:
    - name: http
      port: 80
  selector:
    app: traefiklabs
    task: whoami

---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: test6.route
  namespace: other-ns

spec:
  entryPoints:
    - web
  routes:
  - match: Host(`foo.com`) && PathPrefix(`/a`)
    kind: Rule
    services:
    - name: whoami
      namespace: default
      port: 80
  - match: Host(`foo.com`) && PathPrefix(`/b`)
    kind: Rule
    services:
    - name: wrr2
      namespace: default
      kind: TraefikService
  - match: Host(`foo.com`) && PathPrefix(`/c`)
    kind: Rule
    services:
    - name: wrr3
      kind: TraefikService
  - match: Host(`foo.com`) && PathPrefix(`/d`)
    kind: Rule
    services:
    - name: whoami
      namespace: other-ns
      port: 80
    middlewares:
    - name: stripprefix2
      namespace: default
  - match: Host(`foo.com`) && PathPrefix(`/e`)
    kind: Rule
    services:
    - name: whoami
      port: 80
    middlewares:
    - name: test-errorpage

---
apiVersion: traefik.io/v1alpha1
kind: TraefikService
metadata:
  name: wrr2
  namespace: default

spec:
  weighted:
    services:
      - name: whoami
        port: 80

---
apiVersion: traefik.io/v1alpha1
kind: TraefikService
metadata:
  name: wrr3
  namespace: other-ns

spec:
  weighted:
    services:
      - name: whoami
        namespace: default
        port: 80

---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: stripprefix2
  namespace: default

spec:
  stripPrefix:
    prefixes:
      - /tobestripped

---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: test-errorpage

spec:
  errors:
    status:
      - 500-599
    query: /{status}.html
    service:
      name: whoami
      namespace: other-ns
      port: 80
