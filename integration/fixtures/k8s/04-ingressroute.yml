apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: mychain
  namespace: default

spec:
  chain:
    middlewares:
      - name: stripprefix

---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: stripprefix
  namespace: default

spec:
  stripPrefix:
    prefixes:
      - /tobestripped

---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: test2.route
  namespace: default

spec:
  entryPoints:
    - web
  routes:
  - match: Host(`foo.com`) && PathPrefix(`/tobestripped`)
    kind: Rule
    services:
    - name: whoami
      port: 80
    middlewares:
    - name: mychain
