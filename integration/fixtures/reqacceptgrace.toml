[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[entryPoints]

  [entryPoints.web]
    address = ":8000"
    [entryPoints.web.transport.lifeCycle]
      requestAcceptGraceTimeout = "10s"

  [entryPoints.traefik]
    address = ":8001"
    [entryPoints.traefik.transport.lifeCycle]
      requestAcceptGraceTimeout = "10s"

[ping]

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router]
    service = "service"
    rule = "Path(`/service`)"

[http.services]
    [http.services.service.loadBalancer]
      [[http.services.service.loadBalancer.servers]]
        url = "{{ .Server }}"

