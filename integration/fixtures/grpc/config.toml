[global]
  checkNewVersion = false
  sendAnonymousUsage = false

[log]
  level = "DEBUG"
  noColor = true

[serversTransport]
  rootCAs = [ """{{ .CertContent }}""" ]

[entryPoints]
  [entryPoints.websecure]
    address = ":4443"

[api]
  insecure = true

[providers.file]
  filename = "{{ .SelfFilename }}"

## dynamic configuration ##

[http.routers]
  [http.routers.router1]
    rule = "Host(`127.0.0.1`)"
    service = "service1"
    [http.routers.router1.tls]

[http.services]
  [http.services.service1.loadBalancer]
    [[http.services.service1.loadBalancer.servers]]
      url = "https://127.0.0.1:{{ .GRPCServerPort }}"

[tls.stores]
  [tls.stores.default.defaultCertificate]
    certFile = """{{ .CertContent }}"""
    keyFile  = """{{ .KeyContent }}"""
