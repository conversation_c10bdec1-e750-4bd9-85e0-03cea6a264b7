{"routers": {"api@internal": {"entryPoints": ["traefik"], "service": "api@internal", "rule": "PathPrefix(`/api`)", "ruleSyntax": "default", "priority": 9223372036854775806, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}, "dashboard@internal": {"entryPoints": ["traefik"], "middlewares": ["dashboard_redirect@internal", "dashboard_stripprefix@internal"], "service": "dashboard@internal", "rule": "PathPrefix(`/`)", "ruleSyntax": "default", "priority": 9223372036854775805, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}, "httproute-default-http-app-1-gw-default-my-gateway-ep-web-0-1c0cf64bde37d9d0df06@kubernetesgateway": {"entryPoints": ["web"], "service": "httproute-default-http-app-1-gw-default-my-gateway-ep-web-0-1c0cf64bde37d9d0df06-wrr", "rule": "Host(`foo.com`) && Path(`/bar`)", "ruleSyntax": "default", "priority": 100008, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "httproute-default-http-app-1-gw-default-my-https-gateway-ep-websecure-0-1c0cf64bde37d9d0df06@kubernetesgateway": {"entryPoints": ["websecure"], "service": "httproute-default-http-app-1-gw-default-my-https-gateway-ep-websecure-0-1c0cf64bde37d9d0df06-wrr", "rule": "Host(`foo.com`) && Path(`/bar`)", "ruleSyntax": "default", "priority": 100008, "tls": {}, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["websecure"]}}, "middlewares": {"dashboard_redirect@internal": {"redirectRegex": {"regex": "^(http:\\/\\/(\\[[\\w:.]+\\]|[\\w\\._-]+)(:\\d+)?)\\/$", "replacement": "${1}/dashboard/", "permanent": true}, "status": "enabled", "usedBy": ["dashboard@internal"]}, "dashboard_stripprefix@internal": {"stripPrefix": {"prefixes": ["/dashboard/", "/dashboard"]}, "status": "enabled", "usedBy": ["dashboard@internal"]}}, "services": {"api@internal": {"status": "enabled", "usedBy": ["api@internal"]}, "dashboard@internal": {"status": "enabled", "usedBy": ["dashboard@internal"]}, "default-whoami-http-80@kubernetesgateway": {"loadBalancer": {"servers": [{"url": "http://*********:80"}, {"url": "http://*********:80"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "serverStatus": {"http://*********:80": "UP", "http://*********:80": "UP"}}, "httproute-default-http-app-1-gw-default-my-gateway-ep-web-0-1c0cf64bde37d9d0df06-wrr@kubernetesgateway": {"weighted": {"services": [{"name": "default-whoami-http-80", "weight": 1}]}, "status": "enabled", "usedBy": ["httproute-default-http-app-1-gw-default-my-gateway-ep-web-0-1c0cf64bde37d9d0df06@kubernetesgateway"]}, "httproute-default-http-app-1-gw-default-my-https-gateway-ep-websecure-0-1c0cf64bde37d9d0df06-wrr@kubernetesgateway": {"weighted": {"services": [{"name": "default-whoami-http-80", "weight": 1}]}, "status": "enabled", "usedBy": ["httproute-default-http-app-1-gw-default-my-https-gateway-ep-websecure-0-1c0cf64bde37d9d0df06@kubernetesgateway"]}, "noop@internal": {"status": "enabled"}}, "tcpRouters": {"tcproute-default-tcp-app-1-gw-default-my-tcp-gateway-ep-footcp-0-e3b0c44298fc1c149afb@kubernetesgateway": {"entryPoints": ["footcp"], "service": "tcproute-default-tcp-app-1-gw-default-my-tcp-gateway-ep-footcp-0-e3b0c44298fc1c149afb-wrr", "rule": "HostSNI(`*`)", "ruleSyntax": "default", "priority": -1, "status": "enabled", "using": ["footcp"]}, "tcproute-default-tcp-app-1-gw-default-my-tls-gateway-ep-footlsterminate-0-e3b0c44298fc1c149afb@kubernetesgateway": {"entryPoints": ["footlsterminate"], "service": "tcproute-default-tcp-app-1-gw-default-my-tls-gateway-ep-footlsterminate-0-e3b0c44298fc1c149afb-wrr", "rule": "HostSNI(`*`)", "ruleSyntax": "default", "priority": -1, "tls": {"passthrough": false}, "status": "enabled", "using": ["footlsterminate"]}, "tlsroute-default-tls-app-1-gw-default-my-tls-gateway-ep-footlspassthrough-0-e3b0c44298fc1c149afb@kubernetesgateway": {"entryPoints": ["footlspassthrough"], "service": "tlsroute-default-tls-app-1-gw-default-my-tls-gateway-ep-footlspassthrough-0-e3b0c44298fc1c149afb-wrr", "rule": "HostSNI(`foo.bar`)", "ruleSyntax": "default", "priority": 7, "tls": {"passthrough": true}, "status": "enabled", "using": ["footlspassthrough"]}}, "tcpServices": {"default-whoamitcp-8080@kubernetesgateway": {"loadBalancer": {"servers": [{"address": "*********:8080"}, {"address": "*********:8080"}]}, "status": "enabled"}, "tcproute-default-tcp-app-1-gw-default-my-tcp-gateway-ep-footcp-0-e3b0c44298fc1c149afb-wrr@kubernetesgateway": {"weighted": {"services": [{"name": "default-whoamitcp-8080", "weight": 1}]}, "status": "enabled", "usedBy": ["tcproute-default-tcp-app-1-gw-default-my-tcp-gateway-ep-footcp-0-e3b0c44298fc1c149afb@kubernetesgateway"]}, "tcproute-default-tcp-app-1-gw-default-my-tls-gateway-ep-footlsterminate-0-e3b0c44298fc1c149afb-wrr@kubernetesgateway": {"weighted": {"services": [{"name": "default-whoamitcp-8080", "weight": 1}]}, "status": "enabled", "usedBy": ["tcproute-default-tcp-app-1-gw-default-my-tls-gateway-ep-footlsterminate-0-e3b0c44298fc1c149afb@kubernetesgateway"]}, "tlsroute-default-tls-app-1-gw-default-my-tls-gateway-ep-footlspassthrough-0-e3b0c44298fc1c149afb-wrr@kubernetesgateway": {"weighted": {"services": [{"name": "default-whoamitcp-8080", "weight": 1}]}, "status": "enabled", "usedBy": ["tlsroute-default-tls-app-1-gw-default-my-tls-gateway-ep-footlspassthrough-0-e3b0c44298fc1c149afb@kubernetesgateway"]}}}