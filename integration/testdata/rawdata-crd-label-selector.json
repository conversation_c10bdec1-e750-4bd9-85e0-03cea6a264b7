{"routers": {"default-api-route-29f28a463fb5d5ba16d2@kubernetescrd": {"entryPoints": ["web"], "service": "api@internal", "rule": "PathPrefix(`/api`)", "priority": 18, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "default-test-route-6b204d94623b3df4370c@kubernetescrd": {"entryPoints": ["web"], "service": "default-test-route-6b204d94623b3df4370c", "rule": "Host(`foo.com`) && PathPrefix(`/bar`)", "priority": 12, "tls": {"options": "default-mytlsoption"}, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}}, "services": {"api@internal": {"status": "enabled", "usedBy": ["default-api-route-29f28a463fb5d5ba16d2@kubernetescrd"]}, "dashboard@internal": {"status": "enabled"}, "default-test-route-6b204d94623b3df4370c@kubernetescrd": {"loadBalancer": {"servers": [{"url": "http://*********:80"}, {"url": "http://*********:80"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "usedBy": ["default-test-route-6b204d94623b3df4370c@kubernetescrd"], "serverStatus": {"http://*********:80": "UP", "http://*********:80": "UP"}}, "noop@internal": {"status": "enabled"}}}