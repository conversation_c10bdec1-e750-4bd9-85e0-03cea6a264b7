{"routers": {"Router0@etcd": {"entryPoints": ["web"], "middlewares": ["compressor@etcd", "striper@etcd"], "service": "simplesvc", "rule": "Host(`kv1.localhost`)", "priority": 42, "tls": {}, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "Router1@etcd": {"entryPoints": ["web"], "service": "simplesvc", "rule": "Host(`kv2.localhost`)", "priority": 42, "tls": {"domains": [{"main": "aaa.localhost", "sans": ["aaa.aaa.localhost", "bbb.aaa.localhost"]}, {"main": "bbb.local<PERSON>t", "sans": ["aaa.bbb.localhost", "bbb.bbb.localhost"]}]}, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "api@internal": {"entryPoints": ["traefik"], "service": "api@internal", "rule": "PathPrefix(`/api`)", "ruleSyntax": "default", "priority": 9223372036854775806, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}, "dashboard@internal": {"entryPoints": ["traefik"], "middlewares": ["dashboard_redirect@internal", "dashboard_stripprefix@internal"], "service": "dashboard@internal", "rule": "PathPrefix(`/`)", "ruleSyntax": "default", "priority": 9223372036854775805, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}}, "middlewares": {"compressor@etcd": {"compress": {}, "status": "enabled", "usedBy": ["Router0@etcd"]}, "dashboard_redirect@internal": {"redirectRegex": {"regex": "^(http:\\/\\/(\\[[\\w:.]+\\]|[\\w\\._-]+)(:\\d+)?)\\/$", "replacement": "${1}/dashboard/", "permanent": true}, "status": "enabled", "usedBy": ["dashboard@internal"]}, "dashboard_stripprefix@internal": {"stripPrefix": {"prefixes": ["/dashboard/", "/dashboard"]}, "status": "enabled", "usedBy": ["dashboard@internal"]}, "striper@etcd": {"stripPrefix": {"prefixes": ["foo", "bar"]}, "status": "enabled", "usedBy": ["Router0@etcd"]}}, "services": {"Service03@etcd": {"weighted": {"services": [{"name": "srvcA", "weight": 42}, {"name": "srvcB", "weight": 42}]}, "status": "enabled"}, "api@internal": {"status": "enabled", "usedBy": ["api@internal"]}, "dashboard@internal": {"status": "enabled", "usedBy": ["dashboard@internal"]}, "mirror@etcd": {"mirroring": {"service": "simplesvc", "maxBodySize": -1, "mirrors": [{"name": "srvcA", "percent": 42}, {"name": "srvcB", "percent": 42}]}, "status": "enabled"}, "noop@internal": {"status": "enabled"}, "simplesvc@etcd": {"loadBalancer": {"servers": [{"url": "http://********:8888"}, {"url": "http://********:8889"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "usedBy": ["Router0@etcd", "Router1@etcd"], "serverStatus": {"http://********:8888": "UP", "http://********:8889": "UP"}}, "srvcA@etcd": {"loadBalancer": {"servers": [{"url": "http://********:8888"}, {"url": "http://********:8889"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled"}, "srvcB@etcd": {"loadBalancer": {"servers": [{"url": "http://********:8888"}, {"url": "http://********:8889"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled"}}}