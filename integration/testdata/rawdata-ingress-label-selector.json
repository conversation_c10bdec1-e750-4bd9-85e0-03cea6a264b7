{"routers": {"api@internal": {"entryPoints": ["traefik"], "service": "api@internal", "rule": "PathPrefix(`/api`)", "ruleSyntax": "default", "priority": 9223372036854775806, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}, "dashboard@internal": {"entryPoints": ["traefik"], "middlewares": ["dashboard_redirect@internal", "dashboard_stripprefix@internal"], "service": "dashboard@internal", "rule": "PathPrefix(`/`)", "ruleSyntax": "default", "priority": 9223372036854775805, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}, "default-test-ingress-whoami-test-whoami@kubernetes": {"entryPoints": ["web"], "service": "default-whoami-http", "rule": "Host(`whoami.test`) && PathPrefix(`/whoami`)", "priority": 44, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}}, "middlewares": {"dashboard_redirect@internal": {"redirectRegex": {"regex": "^(http:\\/\\/(\\[[\\w:.]+\\]|[\\w\\._-]+)(:\\d+)?)\\/$", "replacement": "${1}/dashboard/", "permanent": true}, "status": "enabled", "usedBy": ["dashboard@internal"]}, "dashboard_stripprefix@internal": {"stripPrefix": {"prefixes": ["/dashboard/", "/dashboard"]}, "status": "enabled", "usedBy": ["dashboard@internal"]}}, "services": {"api@internal": {"status": "enabled", "usedBy": ["api@internal"]}, "dashboard@internal": {"status": "enabled", "usedBy": ["dashboard@internal"]}, "default-whoami-http@kubernetes": {"loadBalancer": {"servers": [{"url": "http://*********:80"}, {"url": "http://*********:80"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "usedBy": ["default-test-ingress-whoami-test-whoami@kubernetes"], "serverStatus": {"http://*********:80": "UP", "http://*********:80": "UP"}}, "noop@internal": {"status": "enabled"}}}