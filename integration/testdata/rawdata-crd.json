{"routers": {"default-api-route-29f28a463fb5d5ba16d2@kubernetescrd": {"entryPoints": ["web"], "service": "api@internal", "rule": "PathPrefix(`/api`)", "priority": 18, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "default-test-route-6b204d94623b3df4370c@kubernetescrd": {"entryPoints": ["web"], "service": "default-test-route-6b204d94623b3df4370c", "rule": "Host(`foo.com`) && PathPrefix(`/bar`)", "priority": 12, "tls": {"options": "default-mytlsoption"}, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "default-test2-route-23c7f4c450289ee29016@kubernetescrd": {"entryPoints": ["web"], "middlewares": ["default-mychain@kubernetescrd"], "service": "default-test2-route-23c7f4c450289ee29016", "rule": "Host(`foo.com`) && PathPrefix(`/tobestripped`)", "priority": 46, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "default-test3-route-7d0ac22d3d8db4b82618@kubernetescrd": {"entryPoints": ["web"], "service": "default-wrr1", "rule": "Host(`foo.com`) && PathPrefix(`/wrr1`)", "priority": 38, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "default-testst-route-60ad45fcb5fc1f5f3629@kubernetescrd": {"entryPoints": ["web"], "service": "default-testst-route-60ad45fcb5fc1f5f3629", "rule": "Host(`foo.com`) && PathPrefix(`/serverstransport`)", "priority": 50, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "other-ns-test6-route-482e4988e134701d8cc8@kubernetescrd": {"entryPoints": ["web"], "service": "other-ns-wrr3", "rule": "Host(`foo.com`) && PathPrefix(`/c`)", "priority": 35, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "error": ["the service \"other-ns-wrr3@kubernetescrd\" does not exist"], "status": "disabled", "using": ["web"]}}, "middlewares": {"default-mychain@kubernetescrd": {"chain": {"middlewares": ["default-stripprefix@kubernetescrd"]}, "status": "enabled", "usedBy": ["default-test2-route-23c7f4c450289ee29016@kubernetescrd"]}, "default-stripprefix2@kubernetescrd": {"stripPrefix": {"prefixes": ["/tobestripped"]}, "status": "enabled"}, "default-stripprefix@kubernetescrd": {"stripPrefix": {"prefixes": ["/tobestripped"]}, "status": "enabled"}}, "services": {"api@internal": {"status": "enabled", "usedBy": ["default-api-route-29f28a463fb5d5ba16d2@kubernetescrd"]}, "dashboard@internal": {"status": "enabled"}, "default-mirror1@kubernetescrd": {"mirroring": {"service": "default-whoami-80", "mirrors": [{"name": "default-whoami-80"}]}, "status": "enabled"}, "default-test-route-6b204d94623b3df4370c@kubernetescrd": {"loadBalancer": {"servers": [{"url": "http://*********:80"}, {"url": "http://*********:80"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "usedBy": ["default-test-route-6b204d94623b3df4370c@kubernetescrd"], "serverStatus": {"http://*********:80": "UP", "http://*********:80": "UP"}}, "default-test2-route-23c7f4c450289ee29016@kubernetescrd": {"loadBalancer": {"servers": [{"url": "http://*********:80"}, {"url": "http://*********:80"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "usedBy": ["default-test2-route-23c7f4c450289ee29016@kubernetescrd"], "serverStatus": {"http://*********:80": "UP", "http://*********:80": "UP"}}, "default-testst-route-60ad45fcb5fc1f5f3629@kubernetescrd": {"loadBalancer": {"servers": [{"url": "http://*********:80"}, {"url": "http://*********:80"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}, "serversTransport": "default-mytransport@kubernetescrd"}, "status": "enabled", "usedBy": ["default-testst-route-60ad45fcb5fc1f5f3629@kubernetescrd"], "serverStatus": {"http://*********:80": "UP", "http://*********:80": "UP"}}, "default-whoami-80@kubernetescrd": {"loadBalancer": {"servers": [{"url": "http://*********:80"}, {"url": "http://*********:80"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "serverStatus": {"http://*********:80": "UP", "http://*********:80": "UP"}}, "default-wrr1@kubernetescrd": {"weighted": {"services": [{"name": "default-mirror1", "weight": 1}, {"name": "default-whoami-80", "weight": 1}]}, "status": "enabled", "usedBy": ["default-test3-route-7d0ac22d3d8db4b82618@kubernetescrd"]}, "default-wrr2@kubernetescrd": {"weighted": {"services": [{"name": "default-whoami-80", "weight": 1}]}, "status": "enabled"}, "noop@internal": {"status": "enabled"}}, "tcpRouters": {"default-test3.route-673acf455cb2dab0b43a@kubernetescrd": {"entryPoints": ["footcp"], "service": "default-test3.route-673acf455cb2dab0b43a", "rule": "HostSNI(`*`)", "priority": -1, "tls": {"passthrough": false, "options": "default-mytlsoption"}, "status": "enabled", "using": ["footcp"]}}, "tcpServices": {"default-test3.route-673acf455cb2dab0b43a-externalname-svc-9090@kubernetescrd": {"loadBalancer": {"servers": [{"address": "domain.com:9090"}]}, "status": "enabled"}, "default-test3.route-673acf455cb2dab0b43a-whoamitcp-8080@kubernetescrd": {"loadBalancer": {"servers": [{"address": "*********:8080"}, {"address": "*********:8080"}]}, "status": "enabled"}, "default-test3.route-673acf455cb2dab0b43a@kubernetescrd": {"weighted": {"services": [{"name": "default-test3.route-673acf455cb2dab0b43a-whoamitcp-8080", "weight": 1}, {"name": "default-test3.route-673acf455cb2dab0b43a-externalname-svc-9090", "weight": 1}]}, "status": "enabled", "usedBy": ["default-test3.route-673acf455cb2dab0b43a@kubernetescrd"]}}, "udpRouters": {"default-test3.route-0@kubernetescrd": {"entryPoints": ["fooudp"], "service": "default-test3.route-0", "status": "enabled", "using": ["fooudp"]}}, "udpServices": {"default-test3.route-0-externalname-svc-9090@kubernetescrd": {"loadBalancer": {"servers": [{"address": "domain.com:9090"}]}, "status": "enabled"}, "default-test3.route-0-whoamiudp-8090@kubernetescrd": {"loadBalancer": {"servers": [{"address": "*********:8090"}, {"address": "*********:8090"}]}, "status": "enabled"}, "default-test3.route-0@kubernetescrd": {"weighted": {"services": [{"name": "default-test3.route-0-whoamiudp-8090", "weight": 1}, {"name": "default-test3.route-0-externalname-svc-9090", "weight": 1}]}, "status": "enabled", "usedBy": ["default-test3.route-0@kubernetescrd"]}}}