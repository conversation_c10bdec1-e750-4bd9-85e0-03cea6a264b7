{"routers": {"Router0@consul": {"entryPoints": ["web"], "middlewares": ["compressor@consul", "striper@consul"], "service": "simplesvc", "rule": "Host(`kv1.localhost`)", "priority": 42, "tls": {}, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "Router1@consul": {"entryPoints": ["web"], "service": "simplesvc", "rule": "Host(`kv2.localhost`)", "priority": 42, "tls": {"domains": [{"main": "aaa.localhost", "sans": ["aaa.aaa.localhost", "bbb.aaa.localhost"]}, {"main": "bbb.local<PERSON>t", "sans": ["aaa.bbb.localhost", "bbb.bbb.localhost"]}]}, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["web"]}, "api@internal": {"entryPoints": ["traefik"], "service": "api@internal", "rule": "PathPrefix(`/api`)", "ruleSyntax": "default", "priority": 9223372036854775806, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}, "dashboard@internal": {"entryPoints": ["traefik"], "middlewares": ["dashboard_redirect@internal", "dashboard_stripprefix@internal"], "service": "dashboard@internal", "rule": "PathPrefix(`/`)", "ruleSyntax": "default", "priority": 9223372036854775805, "observability": {"accessLogs": true, "tracing": true, "metrics": true}, "status": "enabled", "using": ["traefik"]}}, "middlewares": {"compressor@consul": {"compress": {}, "status": "enabled", "usedBy": ["Router0@consul"]}, "dashboard_redirect@internal": {"redirectRegex": {"regex": "^(http:\\/\\/(\\[[\\w:.]+\\]|[\\w\\._-]+)(:\\d+)?)\\/$", "replacement": "${1}/dashboard/", "permanent": true}, "status": "enabled", "usedBy": ["dashboard@internal"]}, "dashboard_stripprefix@internal": {"stripPrefix": {"prefixes": ["/dashboard/", "/dashboard"]}, "status": "enabled", "usedBy": ["dashboard@internal"]}, "striper@consul": {"stripPrefix": {"prefixes": ["foo", "bar"]}, "status": "enabled", "usedBy": ["Router0@consul"]}}, "services": {"Service03@consul": {"weighted": {"services": [{"name": "srvcA", "weight": 42}, {"name": "srvcB", "weight": 42}]}, "status": "enabled"}, "api@internal": {"status": "enabled", "usedBy": ["api@internal"]}, "dashboard@internal": {"status": "enabled", "usedBy": ["dashboard@internal"]}, "mirror@consul": {"mirroring": {"service": "simplesvc", "maxBodySize": -1, "mirrors": [{"name": "srvcA", "percent": 42}, {"name": "srvcB", "percent": 42}]}, "status": "enabled"}, "noop@internal": {"status": "enabled"}, "simplesvc@consul": {"loadBalancer": {"servers": [{"url": "http://********:8888"}, {"url": "http://********:8889"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled", "usedBy": ["Router0@consul", "Router1@consul"], "serverStatus": {"http://********:8888": "UP", "http://********:8889": "UP"}}, "srvcA@consul": {"loadBalancer": {"servers": [{"url": "http://********:8888"}, {"url": "http://********:8889"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled"}, "srvcB@consul": {"loadBalancer": {"servers": [{"url": "http://********:8888"}, {"url": "http://********:8889"}], "strategy": "wrr", "passHostHeader": true, "responseForwarding": {"flushInterval": "100ms"}}, "status": "enabled"}}}