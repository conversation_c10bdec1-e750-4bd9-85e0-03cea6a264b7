apiVersion: gateway.networking.k8s.io/v1
date: '-'
gatewayAPIChannel: experimental
gatewayAPIVersion: v1.3.0
implementation:
  contact:
  - '@traefik/maintainers'
  organization: traefik
  project: traefik
  url: https://traefik.io/
  version: v3.5
kind: ConformanceReport
mode: default
profiles:
- core:
    result: success
    statistics:
      Failed: 0
      Passed: 12
      Skipped: 0
  name: GATEWAY-GRPC
  summary: Core tests succeeded.
- core:
    result: success
    statistics:
      Failed: 0
      Passed: 33
      Skipped: 0
  extended:
    result: success
    statistics:
      Failed: 0
      Passed: 13
      Skipped: 0
    supportedFeatures:
    - GatewayPort8080
    - HTTPRouteBackendProtocolH2C
    - HTTPRouteBackendProtocolWebSocket
    - HTTPRouteDestinationPortMatching
    - HTTPRouteHostRewrite
    - HTTPRouteMethodMatching
    - HTTPRoutePathRedirect
    - HTTPRoutePathRewrite
    - HTTPRoutePortRedirect
    - HTTPRouteQueryParamMatching
    - HTTPRouteResponseHeaderModification
    - HTTPRouteSchemeRedirect
    unsupportedFeatures:
    - GatewayAddressEmpty
    - GatewayHTTPListenerIsolation
    - GatewayInfrastructurePropagation
    - GatewayStaticAddresses
    - HTTPRouteBackendRequestHeaderModification
    - HTTPRouteBackendTimeout
    - HTTPRouteParentRefPort
    - HTTPRouteRequestMirror
    - HTTPRouteRequestMultipleMirrors
    - HTTPRouteRequestPercentageMirror
    - HTTPRouteRequestTimeout
  name: GATEWAY-HTTP
  summary: Core tests succeeded. Extended tests succeeded.
- core:
    result: success
    statistics:
      Failed: 0
      Passed: 11
      Skipped: 0
  name: GATEWAY-TLS
  summary: Core tests succeeded.
