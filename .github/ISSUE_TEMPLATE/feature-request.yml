name: Feature Request (Traefik)
description: Suggest an idea for this project.
body:
  - type: checkboxes
    id: terms
    attributes:
      label: Welcome!
      description: |
        The issue tracker is for reporting bugs and feature requests only. For end-user related support questions, please refer to one of the following:
        - the Traefik community forum: https://community.traefik.io/

        DO NOT FILE ISSUES FOR GENERAL SUPPORT QUESTIONS.
      options:
        - label: Yes, I've searched similar issues on [GitHub](https://github.com/traefik/traefik/issues) and didn't find any.
          required: true
        - label: Yes, I've searched similar issues on the [Traefik community forum](https://community.traefik.io) and didn't find any.
          required: true

  - type: textarea
    attributes:
      label: What did you expect to see?
      description: |
        How to write a good issue?

        - Respect the issue template as much as possible.
        - The title should be short and descriptive.
        - Explain the conditions which led you to report this issue: the context.
        - The context should lead to something, an idea or a problem that you’re facing.
        - Remain clear and concise.
        - Format your messages to help the reader focus on what matters and understand the structure of your message, use [Markdown syntax](https://help.github.com/articles/github-flavored-markdown)
      placeholder: What did you expect to see?
    validations:
      required: true
