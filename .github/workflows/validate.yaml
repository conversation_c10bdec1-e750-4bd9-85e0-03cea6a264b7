name: Validate

on:
  pull_request:
    branches:
      - '*'

env:
  GO_VERSION: '1.24'
  GOLANGCI_LINT_VERSION: v2.0.2
  MISSPELL_VERSION: v0.6.0

jobs:

  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}
          check-latest: true

      - name: Avoid generating webui
        run: |
          mkdir webui/static
          touch webui/static/index.html

      - name: golangci-lint
        uses: golangci/golangci-lint-action@v7
        with:
          version: "${{ env.GOLANGCI_LINT_VERSION }}"

  validate:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}
          check-latest: true

      - name: Install misspell ${{ env.MISSPELL_VERSION }}
        run: curl -sfL https://raw.githubusercontent.com/golangci/misspell/HEAD/install-misspell.sh | sh -s -- -b $(go env GOPATH)/bin ${MISSPELL_VERSION}

      - name: Avoid generating webui
        run: |
          mkdir webui/static
          touch webui/static/index.html

      - name: Validate
        run: make validate-files

  validate-generate:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}
          check-latest: true

      - name: go generate
        run: |
          make generate
          git diff --exit-code

      - name: go mod tidy
        run: |
          go mod tidy
          git diff --exit-code

      - name: make generate-crd
        run: |
          make generate-crd
          git diff --exit-code
