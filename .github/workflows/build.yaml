name: Build Binaries

on:
  pull_request:
    branches:
      - '*'
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'script/gcg/**'

env:
  GO_VERSION: '1.24'
  CGO_ENABLED: 0

jobs:

  build-webui:
    uses: ./.github/workflows/template-webui.yaml

  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        os: [ darwin, freebsd, linux, openbsd, windows ]
        arch: [ amd64, arm64 ]
        include:
          - os: freebsd
            arch: 386
          - os: linux
            arch: 386
          - os: linux
            arch: arm
            goarm: 6
          - os: linux
            arch: arm
            goarm: 7
          - os: linux
            arch: ppc64le
          - os: linux
            arch: riscv64
          - os: linux
            arch: s390x
          - os: openbsd
            arch: 386
          - os: windows
            arch: 386
    needs:
      - build-webui

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@v5
        env:
          ImageOS: ${{ matrix.os }}-${{ matrix.arch }}-${{ matrix.goarm }}
        with:
          go-version: ${{ env.GO_VERSION }}
          check-latest: true

      - name: Artifact webui
        uses: actions/download-artifact@v4
        with:
          name: webui.tar.gz

      - name: Untar webui
        run: |
          tar xvf webui.tar.gz
          rm webui.tar.gz

      - name: Build
        env:
          GOOS: ${{ matrix.os }}
          GOARCH: ${{ matrix.arch }}
          GOARM: ${{ matrix.goarm }}
        run: make binary
