name: Sync Docker Images

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *" # Run every day

jobs:
  sync:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    if: github.repository == 'traefik/traefik'

    steps:
      - uses: actions/checkout@v4

      - uses: imjasonh/setup-crane@v0.4
      
      - name: Sync
        run: |
          EXCLUDED_TAGS="1.7.9-alpine v1.0.0-beta.392 v1.0.0-beta.404 v1.0.0-beta.704 v1.0.0-rc1 v1.7.9-alpine"
          EXCLUDED_REGEX=$(echo $EXCLUDED_TAGS | sed 's/ /|/g')
          diff <(crane ls traefik) <(crane ls ghcr.io/traefik/traefik) | grep '^<' | awk '{print $2}' | while read -r tag; do [[ "$tag" =~ ^($EXCLUDED_REGEX)$ ]] || (echo "Processing image: traefik:$tag"; crane cp "traefik:$tag" "ghcr.io/traefik/traefik:$tag"); done
          crane cp traefik:latest ghcr.io/traefik/traefik:latest
