name: Build and Publish Documentation

on:
  push:
    branches:
      - master
      - v*

env:
  STRUCTOR_VERSION: v1.13.2
  MIXTUS_VERSION: v0.4.1

jobs:

  docs:
    name: Doc Process
    runs-on: ubuntu-latest
    if: github.repository == 'traefik/traefik'

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Install Structor ${{ env.STRUCTOR_VERSION }}
        run: curl -sSfL https://raw.githubusercontent.com/traefik/structor/master/godownloader.sh | sh -s -- -b $HOME/bin ${STRUCTOR_VERSION}

      - name: Install Seo-doc
        run: curl -sSfL https://raw.githubusercontent.com/traefik/seo-doc/master/godownloader.sh | sh -s -- -b "${HOME}/bin"

      - name: Install Mixtus ${{ env.MIXTUS_VERSION }}
        run: curl -sSfL https://raw.githubusercontent.com/traefik/mixtus/master/godownloader.sh | sh -s -- -b $HOME/bin ${MIXTUS_VERSION}

      - name: Build documentation
        run: $HOME/bin/structor -o traefik -r traefik --dockerfile-url="https://raw.githubusercontent.com/traefik/traefik/v1.7/docs.Dockerfile" --menu.js-url="https://raw.githubusercontent.com/traefik/structor/master/traefik-menu.js.gotmpl" --rqts-url="https://raw.githubusercontent.com/traefik/structor/master/requirements-override.txt" --force-edit-url --exp-branch=master --debug
        env:
          STRUCTOR_LATEST_TAG: ${{ vars.STRUCTOR_LATEST_TAG }}

      - name: Apply seo
        run: $HOME/bin/seo -path=./site -product=traefik

      - name: Publish documentation
        run: $HOME/bin/mixtus --dst-doc-path="./traefik" --dst-owner=traefik --dst-repo-name=doc --git-user-email="<EMAIL>" --git-user-name=traefiker --src-doc-path="./site" --src-owner=traefik --src-repo-name=traefik
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_REPO }}
