name: Check Documentation

on:
  pull_request:
    branches:
      - '*'

jobs:

  docs:
    name: Check, verify and build documentation
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check documentation
        run: make docs-pull-images docs
        env:
          # These variables are not passed to workflows that are triggered by a pull request from a fork.
          DOCS_VERIFY_SKIP: ${{ vars.DOCS_VERIFY_SKIP }}
          DOCS_LINT_SKIP: ${{ vars.DOCS_LINT_SKIP }}
