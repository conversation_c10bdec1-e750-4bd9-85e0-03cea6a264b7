<!-- PLEASE FOLLOW THE ISSUE TEMPLATE TO HELP TRIAGE AND SUPPORT! -->

### Do you want to request a *feature* or report a *bug*?

<!--
DO NOT FILE ISSUES FOR GENERAL SUPPORT QUESTIONS.

The issue tracker is for reporting bugs and feature requests only.
For end-user related support questions, please refer to one of the following:

- the Traefik community forum: https://community.traefik.io/

-->

Bug

<!--

The configurations between 1.X and 2.X are NOT compatible.
Please have a look here https://doc.traefik.io/traefik/getting-started/configuration-overview/.

-->

### What did you do?

<!--

HOW TO WRITE A GOOD BUG REPORT?

- Respect the issue template as much as possible.
- The title should be short and descriptive.
- Explain the conditions which led you to report this issue: the context.
- The context should lead to something, an idea or a problem that you’re facing.
- Remain clear and concise.
- Format your messages to help the reader focus on what matters and understand the structure of your message, use Markdown syntax https://help.github.com/articles/github-flavored-markdown

-->

### What did you expect to see?



### What did you see instead?



### Output of `traefik version`: (_What version of Traefik are you using?_)

<!--
`latest` is not considered as a valid version.

For the Traefik Docker image:
    docker run [IMAGE] version
    ex: docker run traefik version

-->

```
(paste your output here)
```

### What is your environment & configuration (arguments, toml, provider, platform, ...)?

```toml
# (paste your configuration here)
```

<!--
Add more configuration information here.
-->


### If applicable, please paste the log output in DEBUG level (`--log.level=DEBUG` switch)

```
(paste your output here)
```
